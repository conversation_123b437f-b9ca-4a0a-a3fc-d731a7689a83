import pandas as pd
import numpy as np
import os
import logging
from datetime import datetime

class AShareDataProcessor:
    """A股市场数据处理器"""
    
    def __init__(self, index_type='hs300', top_stocks=None, feature_subset=None):
        """
        初始化数据处理器
        
        Args:
            index_type (str): 指数类型，可选 'hs300', 'zz500', 'zz1000'
            top_stocks (int): 选择交易量最大的前N支股票，若为None则使用全部股票
            feature_subset (list): 要使用的特征子集列表，若为None则使用全部特征
        """
        self.index_type = index_type
        self.top_stocks = top_stocks
        self.feature_subset = feature_subset
        self.logger = logging.getLogger(__name__)
        # 使用绝对路径
        #self.base_dir = os.path.abspath(os.path.join('/tmp/pycharm_project_525/alphaportfolio-ashare/dataset', index_type))
        self.base_dir = os.path.abspath(
            os.path.join('dataset', index_type))
        self.logger.info(f"数据集基础目录: {self.base_dir}")
        
        # 记录可选的核心特征集合，使用实际数据集中存在的字段
        self.core_features = [
            'close', 'turnover', 'total_vol', 'ret', 
            'std_turnover', 'std_vol', 'high_low_mean', 
            'turn_cov_1m', 'price_range_1m', 'mom_1m'
        ]
        
    def _get_date_range_dirs(self, start_year, end_year):
        """
        获取指定年份范围内的日期范围目录列表
        
        Args:
            start_year (int): 起始年份
            end_year (int): 结束年份
            
        Returns:
            list: 日期范围目录路径列表
        """
        date_dirs = []
        # 遍历基础目录下的所有目录
        for date_dir in os.listdir(self.base_dir):
            dir_path = os.path.join(self.base_dir, date_dir)
            if not os.path.isdir(dir_path):
                continue
                
            # 从目录名中提取年份
            try:
                dir_year = int(date_dir.split('-')[0])
                if start_year <= dir_year <= end_year:
                    date_dirs.append(dir_path)
            except (ValueError, IndexError):
                self.logger.warning(f"无法从目录名 {date_dir} 中提取年份")
                continue
                
        if not date_dirs:
            self.logger.error(f"在 {start_year} 到 {end_year} 范围内没有找到任何日期范围目录")
        return date_dirs
        
    def _get_data_file(self, date_dir):
        """
        获取指定日期范围目录下的数据文件
        
        Args:
            date_dir (str): 日期范围目录路径
            
        Returns:
            str: 数据文件路径，如果不存在则返回None
        """
        # 从目录名中提取日期范围
        date_range = os.path.basename(date_dir)
        start_date = date_range.split('_')[0].replace('-', '')
        end_date = date_range.split('_')[1].replace('-', '')
        
        # 构建数据文件名
        file_name = f'factors_{self.index_type}_{start_date}_{end_date}.csv'
        file_path = os.path.join(date_dir, file_name)
        
        if os.path.exists(file_path):
            return file_path
        else:
            self.logger.warning(f"数据文件不存在: {file_path}")
            return None
        
    def prepare_training_data(self, start_year, end_year):
        """
        准备训练数据
        
        Args:
            start_year (int): 起始年份
            end_year (int): 结束年份
            
        Returns:
            pd.DataFrame: 处理后的训练数据
        """
        try:
            # 获取日期范围目录列表
            date_dirs = self._get_date_range_dirs(start_year, end_year)
            if not date_dirs:
                return None

            # 读取并合并所有数据文件
            dfs = []
            for date_dir in date_dirs:
                file_path = self._get_data_file(date_dir)
                if file_path:
                    self.logger.info(f"读取数据文件: {file_path}")
                    try:
                        df = pd.read_csv(file_path)
                        # 检查并打印缺失字段
                        required_columns = ['trade_date', 'ts_code', 'close', 'turnover']
                        missing_columns = [col for col in required_columns if col not in df.columns]
                        if missing_columns:
                            self.logger.warning(f"文件 {file_path} 缺少以下字段: {missing_columns}")
                        else:
                            dfs.append(df)
                    except Exception as e:
                        self.logger.error(f"读取文件 {file_path} 失败: {str(e)}")
                        continue

            if not dfs:
                self.logger.error("没有成功读取任何数据文件")
                return None

            # 合并数据
            data = pd.concat(dfs, ignore_index=True)

            # 打印合并后数据的字段
            self.logger.info(f"合并后数据的字段: {data.columns.tolist()}")

            # 转换日期格式
            data['trade_date'] = pd.to_datetime(data['trade_date'])

            # 按日期和股票代码排序
            data = data.sort_values(['trade_date', 'ts_code'])

            # 处理缺失值
            data = data.ffill()

            # 计算技术因子
            data = self.calculate_technical_factors(data)
            
            # 如果指定了top_stocks，选择交易量最大的前N支股票
            if self.top_stocks is not None and self.top_stocks > 0:
                # 计算每支股票的平均交易量
                stock_volume = data.groupby('ts_code')['total_vol'].mean().reset_index()
                # 按交易量排序并选择前N支
                top_stocks = stock_volume.sort_values('total_vol', ascending=False).head(self.top_stocks)['ts_code'].tolist()
                # 过滤数据
                data = data[data['ts_code'].isin(top_stocks)]
                self.logger.info(f"已选择交易量最大的前{self.top_stocks}支股票，实际股票数量: {len(top_stocks)}")
            
            # 如果指定了feature_subset，只保留指定的特征
            if self.feature_subset is not None:
                # 确保必要的列存在
                essential_columns = ['trade_date', 'ts_code']
                all_required_columns = essential_columns + self.feature_subset
                # 检查所有所需列是否存在
                missing_features = [col for col in self.feature_subset if col not in data.columns]
                if missing_features:
                    self.logger.warning(f"指定的特征子集中有缺失特征: {missing_features}")
                    # 从feature_subset中移除缺失的特征
                    self.feature_subset = [col for col in self.feature_subset if col in data.columns]
                
                # 只保留必要的列和选定的特征
                all_kept_columns = essential_columns + self.feature_subset
                data = data[all_kept_columns]
                self.logger.info(f"已选择指定的特征子集，保留特征: {self.feature_subset}")
            
            if data is not None:
                self.logger.info(f"成功处理数据，共 {len(data)} 行，股票数: {data['ts_code'].nunique()}，特征数: {len(data.columns) - 2}")
                
            return data
        
        except Exception as e:
            self.logger.error(f"准备训练数据失败: {str(e)}")
            return None
            
    def prepare_test_data(self, test_year):
        """
        准备测试数据
        
        Args:
            test_year (int): 测试年份
            
        Returns:
            pd.DataFrame: 处理后的测试数据
        """
        try:
            # 获取测试年份的日期范围目录
            test_date_dirs = self._get_date_range_dirs(test_year, test_year)
            if not test_date_dirs:
                return None

            # 读取并合并所有数据文件
            dfs = []
            for date_dir in test_date_dirs:
                file_path = self._get_data_file(date_dir)
                if file_path:
                    self.logger.info(f"读取测试数据文件: {file_path}")
                    try:
                        df = pd.read_csv(file_path)
                        dfs.append(df)
                    except Exception as e:
                        self.logger.error(f"读取文件 {file_path} 失败: {str(e)}")
                        continue

            if not dfs:
                self.logger.error("没有成功读取任何测试数据文件")
                return None

            # 合并数据
            data = pd.concat(dfs, ignore_index=True)

            # 转换日期格式
            data['trade_date'] = pd.to_datetime(data['trade_date'])

            # 按日期和股票代码排序
            data = data.sort_values(['trade_date', 'ts_code'])

            # 处理缺失值
            data = data.fillna(method='ffill')

            # 计算技术因子
            data = self.calculate_technical_factors(data)
            
            # 如果指定了top_stocks，选择交易量最大的前N支股票
            if self.top_stocks is not None and self.top_stocks > 0:
                # 计算每支股票的平均交易量
                stock_volume = data.groupby('ts_code')['total_vol'].mean().reset_index()
                # 按交易量排序并选择前N支
                top_stocks = stock_volume.sort_values('total_vol', ascending=False).head(self.top_stocks)['ts_code'].tolist()
                # 过滤数据
                data = data[data['ts_code'].isin(top_stocks)]
                self.logger.info(f"已选择交易量最大的前{self.top_stocks}支股票")
            
            # 如果指定了feature_subset，只保留指定的特征
            if self.feature_subset is not None:
                # 确保必要的列存在
                essential_columns = ['trade_date', 'ts_code']
                all_required_columns = essential_columns + self.feature_subset
                # 检查所有所需列是否存在
                missing_features = [col for col in self.feature_subset if col not in data.columns]
                if missing_features:
                    self.logger.warning(f"指定的特征子集中有缺失特征: {missing_features}")
                    # 从feature_subset中移除缺失的特征
                    self.feature_subset = [col for col in self.feature_subset if col in data.columns]
                
                # 只保留必要的列和选定的特征
                all_kept_columns = essential_columns + self.feature_subset
                data = data[all_kept_columns]
                self.logger.info(f"已选择指定的特征子集，保留特征: {self.feature_subset}")

            self.logger.info(f"成功处理测试数据，共 {len(data)} 行")
            return data
            
        except Exception as e:
            self.logger.error(f"准备测试数据失败: {str(e)}")
            return None
            
    def calculate_technical_factors(self, df):
        """
        计算技术因子
        
        Args:
            df (pd.DataFrame): 原始数据
            
        Returns:
            pd.DataFrame: 添加技术因子后的数据
        """
        try:
            # 确保数据按日期和股票代码排序
            if 'trade_date' not in df.columns or 'ts_code' not in df.columns:
                self.logger.error("数据中缺少必要的列: trade_date 或 ts_code")
                return None

            df = df.sort_values(['trade_date', 'ts_code'])

            # 计算收益率
            if 'close' in df.columns:
                df['return'] = df.groupby('ts_code')['close'].pct_change()
            else:
                self.logger.error("数据中缺少 close 列")
                return None

            # 计算波动率（20日）
            if 'return' in df.columns:
                df['volatility'] = df.groupby('ts_code')['return'].transform(lambda x: x.rolling(20).std())
            else:
                self.logger.error("数据中缺少 return 列")
                return None

            # 计算换手率
            if 'turnover' in df.columns:
                turnover_mean = df.groupby('ts_code')['turnover'].transform(lambda x: x.rolling(20).mean())
                df['turnover_rate'] = df['turnover'] / turnover_mean
            else:
                self.logger.error("数据中缺少 turnover 列")
                return None

            # 计算MACD
            if 'close' in df.columns:
                exp1 = df.groupby('ts_code')['close'].transform(lambda x: x.ewm(span=12, adjust=False).mean())
                exp2 = df.groupby('ts_code')['close'].transform(lambda x: x.ewm(span=26, adjust=False).mean())
                df['MACD'] = exp1 - exp2
            else:
                self.logger.error("数据中缺少 close 列")
                return None

            # 计算MACD信号线
            if 'MACD' in df.columns:
                df['MACD_signal'] = df.groupby('ts_code')['MACD'].transform(lambda x: x.ewm(span=9, adjust=False).mean())
            else:
                self.logger.error("数据中缺少 MACD 列")
                return None

            # 计算RSI
            if 'close' in df.columns:
                # 计算价格变化
                df['price_change'] = df.groupby('ts_code')['close'].transform(lambda x: x.diff())

                # 计算上涨和下跌
                df['gain'] = df['price_change'].where(df['price_change'] > 0, 0)
                df['loss'] = -df['price_change'].where(df['price_change'] < 0, 0)

                # 计算平均上涨和下跌
                df['avg_gain'] = df.groupby('ts_code')['gain'].transform(lambda x: x.rolling(window=14).mean())
                df['avg_loss'] = df.groupby('ts_code')['loss'].transform(lambda x: x.rolling(window=14).mean())

                # 计算RSI
                rs = df['avg_gain'] / df['avg_loss']
                df['RSI'] = 100 - (100 / (1 + rs))

                # 清理临时列
                df = df.drop(['price_change', 'gain', 'loss', 'avg_gain', 'avg_loss'], axis=1)
            else:
                self.logger.error("数据中缺少 close 列")
                return None

            # 处理计算后的缺失值
            df = df.ffill()

            return df
            
        except Exception as e:
            self.logger.error(f"计算技术因子失败: {str(e)}")
            return None 

    def get_core_features(self):
        """
        获取核心特征列表
        
        Returns:
            list: 核心特征列表
        """
        return self.core_features 