"""
Advanced Training Monitor for RL Models

This module provides comprehensive monitoring capabilities including:
1. Real-time training metrics tracking
2. TD-error and value function analysis
3. Training stability monitoring
4. Reward curve analysis and smoothing
5. Exploration rate tracking
6. TensorBoard integration
7. Early stopping recommendations
"""

import os
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from collections import deque, defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.signal import savgol_filter
import torch
import torch.nn.functional as F
from torch.utils.tensorboard import SummaryWriter
import warnings
warnings.filterwarnings('ignore')


class TrainingStabilityAnalyzer:
    """Analyzes training stability and convergence"""
    
    def __init__(self, window_size: int = 50):
        self.window_size = window_size
        self.metrics_history = defaultdict(list)
        self.stability_scores = {}
        
    def add_metric(self, name: str, value: float, episode: int):
        """Add a metric value"""
        self.metrics_history[name].append((episode, value))
        
        # Keep only recent values for stability analysis
        if len(self.metrics_history[name]) > self.window_size * 2:
            self.metrics_history[name] = self.metrics_history[name][-self.window_size * 2:]
    
    def calculate_stability_score(self, name: str) -> float:
        """Calculate stability score for a metric (0-1, higher is more stable)"""
        if name not in self.metrics_history or len(self.metrics_history[name]) < 10:
            return 0.0
        
        values = [v for _, v in self.metrics_history[name][-self.window_size:]]
        
        if len(values) < 10:
            return 0.0
        
        # Calculate various stability metrics
        cv = np.std(values) / (np.abs(np.mean(values)) + 1e-8)  # Coefficient of variation
        
        # Trend stability (how consistent is the trend)
        if len(values) >= 20:
            x = np.arange(len(values))
            slope, _, r_value, _, _ = stats.linregress(x, values)
            trend_stability = abs(r_value)  # How linear is the trend
        else:
            trend_stability = 0.0
        
        # Local stability (smoothness)
        if len(values) >= 5:
            diffs = np.diff(values)
            local_stability = 1.0 / (1.0 + np.std(diffs))
        else:
            local_stability = 0.0
        
        # Combined stability score
        stability = (0.4 * (1.0 / (1.0 + cv)) + 
                    0.3 * trend_stability + 
                    0.3 * local_stability)
        
        self.stability_scores[name] = stability
        return stability
    
    def get_convergence_status(self, name: str, threshold: float = 0.01) -> Dict[str, Any]:
        """Check if metric has converged"""
        if name not in self.metrics_history or len(self.metrics_history[name]) < 20:
            return {'converged': False, 'confidence': 0.0}
        
        values = [v for _, v in self.metrics_history[name][-20:]]
        
        # Check if recent values are stable
        recent_std = np.std(values[-10:])
        overall_std = np.std(values)
        
        # Check if improvement has plateaued
        first_half = np.mean(values[:10])
        second_half = np.mean(values[-10:])
        improvement = abs(second_half - first_half) / (abs(first_half) + 1e-8)
        
        converged = (recent_std < threshold and improvement < threshold)
        confidence = min(1.0, (threshold / (recent_std + 1e-8)) * (threshold / (improvement + 1e-8)))
        
        return {
            'converged': converged,
            'confidence': confidence,
            'recent_std': recent_std,
            'improvement': improvement,
            'stability_score': self.calculate_stability_score(name)
        }


class TDErrorAnalyzer:
    """Analyzes TD-error and value function estimation"""
    
    def __init__(self, capacity: int = 1000):
        self.capacity = capacity
        self.td_errors = deque(maxlen=capacity)
        self.value_estimates = deque(maxlen=capacity)
        self.target_values = deque(maxlen=capacity)
        
    def add_td_error(self, td_error: float, value_estimate: float, target_value: float):
        """Add TD-error data point"""
        self.td_errors.append(td_error)
        self.value_estimates.append(value_estimate)
        self.target_values.append(target_value)
    
    def get_td_statistics(self) -> Dict[str, float]:
        """Get TD-error statistics"""
        if not self.td_errors:
            return {}
        
        td_array = np.array(self.td_errors)
        
        return {
            'td_mean': np.mean(td_array),
            'td_std': np.std(td_array),
            'td_abs_mean': np.mean(np.abs(td_array)),
            'td_max': np.max(td_array),
            'td_min': np.min(td_array),
            'value_bias': np.mean(np.array(self.value_estimates) - np.array(self.target_values))
        }
    
    def detect_value_function_issues(self) -> Dict[str, Any]:
        """Detect potential issues with value function estimation"""
        if len(self.td_errors) < 50:
            return {'issues': []}
        
        issues = []
        stats = self.get_td_statistics()
        
        # Check for high TD-error variance
        if stats['td_std'] > 2.0:
            issues.append({
                'type': 'high_variance',
                'severity': 'high' if stats['td_std'] > 5.0 else 'medium',
                'description': f"High TD-error variance: {stats['td_std']:.3f}"
            })
        
        # Check for systematic bias
        if abs(stats['value_bias']) > 1.0:
            issues.append({
                'type': 'value_bias',
                'severity': 'high' if abs(stats['value_bias']) > 2.0 else 'medium',
                'description': f"Value function bias: {stats['value_bias']:.3f}"
            })
        
        # Check for exploding TD-errors
        if stats['td_abs_mean'] > 10.0:
            issues.append({
                'type': 'exploding_errors',
                'severity': 'critical',
                'description': f"Very high TD-errors: {stats['td_abs_mean']:.3f}"
            })
        
        return {'issues': issues, 'stats': stats}


class RewardCurveAnalyzer:
    """Analyzes reward curves and training progress"""
    
    def __init__(self, smoothing_window: int = 10):
        self.smoothing_window = smoothing_window
        self.rewards = []
        self.episodes = []
        
    def add_reward(self, episode: int, reward: float):
        """Add reward data point"""
        self.episodes.append(episode)
        self.rewards.append(reward)
    
    def get_smoothed_rewards(self, method: str = 'exponential') -> np.ndarray:
        """Get smoothed reward curve"""
        if len(self.rewards) < 3:
            return np.array(self.rewards)
        
        rewards_array = np.array(self.rewards)
        
        if method == 'exponential':
            # Exponential moving average
            alpha = 2.0 / (self.smoothing_window + 1)
            smoothed = np.zeros_like(rewards_array)
            smoothed[0] = rewards_array[0]
            
            for i in range(1, len(rewards_array)):
                smoothed[i] = alpha * rewards_array[i] + (1 - alpha) * smoothed[i-1]
            
            return smoothed
            
        elif method == 'savgol':
            # Savitzky-Golay filter
            if len(rewards_array) >= 5:
                window = min(len(rewards_array) // 4 * 2 + 1, 51)  # Odd number
                return savgol_filter(rewards_array, window, 3)
            else:
                return rewards_array
                
        elif method == 'rolling':
            # Simple rolling average
            return pd.Series(rewards_array).rolling(
                window=min(self.smoothing_window, len(rewards_array)), 
                center=True
            ).mean().fillna(method='bfill').fillna(method='ffill').values
        
        return rewards_array
    
    def detect_training_issues(self) -> Dict[str, Any]:
        """Detect training issues from reward curve"""
        if len(self.rewards) < 20:
            return {'issues': []}
        
        issues = []
        rewards_array = np.array(self.rewards)
        
        # Check for stagnation
        recent_rewards = rewards_array[-10:]
        if np.std(recent_rewards) < 0.01 and len(self.rewards) > 50:
            issues.append({
                'type': 'stagnation',
                'severity': 'medium',
                'description': f"Reward stagnation detected (std: {np.std(recent_rewards):.4f})"
            })
        
        # Check for instability (high variance)
        overall_std = np.std(rewards_array)
        mean_reward = np.mean(rewards_array)
        cv = overall_std / (abs(mean_reward) + 1e-8)
        
        if cv > 2.0:
            issues.append({
                'type': 'instability',
                'severity': 'high' if cv > 5.0 else 'medium',
                'description': f"High reward variance (CV: {cv:.2f})"
            })
        
        # Check for divergence
        if len(rewards_array) >= 50:
            first_quarter = np.mean(rewards_array[:len(rewards_array)//4])
            last_quarter = np.mean(rewards_array[-len(rewards_array)//4:])
            
            if last_quarter < first_quarter - 2 * overall_std:
                issues.append({
                    'type': 'divergence',
                    'severity': 'critical',
                    'description': f"Performance degradation detected"
                })
        
        return {'issues': issues, 'cv': cv, 'mean_reward': mean_reward}


class AdvancedTrainingMonitor:
    """Advanced training monitor with comprehensive analysis"""
    
    def __init__(self, 
                 save_dir: str = 'monitoring',
                 enable_tensorboard: bool = True,
                 stability_window: int = 50):
        """
        Initialize advanced training monitor
        
        Args:
            save_dir: Directory to save monitoring results
            enable_tensorboard: Whether to enable TensorBoard logging
            stability_window: Window size for stability analysis
        """
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # Initialize analyzers
        self.stability_analyzer = TrainingStabilityAnalyzer(stability_window)
        self.td_analyzer = TDErrorAnalyzer()
        self.reward_analyzer = RewardCurveAnalyzer()
        
        # TensorBoard setup
        self.enable_tensorboard = enable_tensorboard
        if enable_tensorboard:
            self.tb_writer = SummaryWriter(os.path.join(save_dir, 'tensorboard'))
        
        # Logging setup
        self.logger = logging.getLogger(__name__)
        self.setup_logging()
        
        # Monitoring data
        self.episode_data = []
        self.current_episode = 0
        
        # Early stopping tracking
        self.best_metric_value = float('-inf')
        self.episodes_without_improvement = 0
        self.early_stopping_recommendations = []
        
    def setup_logging(self):
        """Setup logging for monitor"""
        log_file = os.path.join(self.save_dir, 'monitor.log')
        handler = logging.FileHandler(log_file)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def log_episode(self, 
                   episode: int,
                   reward: float,
                   loss: float,
                   epsilon: float = None,
                   td_error: float = None,
                   value_estimate: float = None,
                   target_value: float = None,
                   additional_metrics: Dict[str, float] = None):
        """
        Log data for an episode
        
        Args:
            episode: Episode number
            reward: Episode reward
            loss: Training loss
            epsilon: Exploration rate
            td_error: TD-error
            value_estimate: Value function estimate
            target_value: Target value
            additional_metrics: Additional metrics to log
        """
        self.current_episode = episode
        
        # Store episode data
        episode_info = {
            'episode': episode,
            'reward': reward,
            'loss': loss,
            'epsilon': epsilon,
            'timestamp': datetime.now().isoformat()
        }
        
        if additional_metrics:
            episode_info.update(additional_metrics)
        
        self.episode_data.append(episode_info)
        
        # Update analyzers
        self.stability_analyzer.add_metric('reward', reward, episode)
        self.stability_analyzer.add_metric('loss', loss, episode)
        self.reward_analyzer.add_reward(episode, reward)
        
        if epsilon is not None:
            self.stability_analyzer.add_metric('epsilon', epsilon, episode)
        
        if td_error is not None and value_estimate is not None and target_value is not None:
            self.td_analyzer.add_td_error(td_error, value_estimate, target_value)
        
        # TensorBoard logging
        if self.enable_tensorboard:
            self.tb_writer.add_scalar('Training/Reward', reward, episode)
            self.tb_writer.add_scalar('Training/Loss', loss, episode)
            
            if epsilon is not None:
                self.tb_writer.add_scalar('Training/Epsilon', epsilon, episode)
            
            if td_error is not None:
                self.tb_writer.add_scalar('Training/TD_Error', td_error, episode)
            
            if additional_metrics:
                for key, value in additional_metrics.items():
                    self.tb_writer.add_scalar(f'Metrics/{key}', value, episode)
        
        # Check for early stopping
        self.check_early_stopping(reward, episode)
    
    def check_early_stopping(self, current_metric: float, episode: int):
        """Check if early stopping should be recommended"""
        if current_metric > self.best_metric_value:
            self.best_metric_value = current_metric
            self.episodes_without_improvement = 0
        else:
            self.episodes_without_improvement += 1
        
        # Analyze convergence
        convergence_info = self.stability_analyzer.get_convergence_status('reward')
        
        if convergence_info['converged'] and convergence_info['confidence'] > 0.8:
            if episode not in [r['episode'] for r in self.early_stopping_recommendations]:
                self.early_stopping_recommendations.append({
                    'episode': episode,
                    'reason': 'convergence',
                    'confidence': convergence_info['confidence'],
                    'description': 'Reward has converged with high confidence'
                })
        
        # Check for stagnation
        if self.episodes_without_improvement > 50:
            if episode not in [r['episode'] for r in self.early_stopping_recommendations]:
                self.early_stopping_recommendations.append({
                    'episode': episode,
                    'reason': 'stagnation',
                    'episodes_without_improvement': self.episodes_without_improvement,
                    'description': f'No improvement for {self.episodes_without_improvement} episodes'
                })
    
    def get_training_analysis(self) -> Dict[str, Any]:
        """Get comprehensive training analysis"""
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'current_episode': self.current_episode,
            'total_episodes': len(self.episode_data)
        }
        
        # Stability analysis
        stability_scores = {}
        for metric in ['reward', 'loss', 'epsilon']:
            stability_scores[metric] = self.stability_analyzer.calculate_stability_score(metric)
        
        analysis['stability'] = {
            'scores': stability_scores,
            'overall_stability': np.mean(list(stability_scores.values()))
        }
        
        # Convergence analysis
        convergence_status = {}
        for metric in ['reward', 'loss']:
            convergence_status[metric] = self.stability_analyzer.get_convergence_status(metric)
        
        analysis['convergence'] = convergence_status
        
        # TD-error analysis
        analysis['td_analysis'] = self.td_analyzer.detect_value_function_issues()
        
        # Reward curve analysis
        analysis['reward_analysis'] = self.reward_analyzer.detect_training_issues()
        
        # Early stopping recommendations
        analysis['early_stopping'] = {
            'recommendations': self.early_stopping_recommendations,
            'episodes_without_improvement': self.episodes_without_improvement,
            'best_metric_value': self.best_metric_value
        }
        
        return analysis
    
    def generate_monitoring_report(self) -> str:
        """Generate comprehensive monitoring report"""
        analysis = self.get_training_analysis()
        
        report_lines = [
            "=" * 80,
            "ADVANCED TRAINING MONITORING REPORT",
            "=" * 80,
            f"Generated: {analysis['timestamp']}",
            f"Current Episode: {analysis['current_episode']}",
            f"Total Episodes Logged: {analysis['total_episodes']}",
            "",
            "STABILITY ANALYSIS",
            "-" * 40
        ]
        
        # Stability scores
        for metric, score in analysis['stability']['scores'].items():
            status = "STABLE" if score > 0.7 else "UNSTABLE" if score < 0.3 else "MODERATE"
            report_lines.append(f"{metric.upper()} Stability: {score:.3f} ({status})")
        
        report_lines.extend([
            f"Overall Stability: {analysis['stability']['overall_stability']:.3f}",
            "",
            "CONVERGENCE ANALYSIS",
            "-" * 40
        ])
        
        # Convergence status
        for metric, status in analysis['convergence'].items():
            converged = "YES" if status['converged'] else "NO"
            report_lines.append(f"{metric.upper()} Converged: {converged} (confidence: {status['confidence']:.3f})")
        
        # TD-error issues
        if analysis['td_analysis']['issues']:
            report_lines.extend([
                "",
                "TD-ERROR ISSUES",
                "-" * 40
            ])
            for issue in analysis['td_analysis']['issues']:
                report_lines.append(f"[{issue['severity'].upper()}] {issue['description']}")
        
        # Reward curve issues
        if analysis['reward_analysis']['issues']:
            report_lines.extend([
                "",
                "REWARD CURVE ISSUES",
                "-" * 40
            ])
            for issue in analysis['reward_analysis']['issues']:
                report_lines.append(f"[{issue['severity'].upper()}] {issue['description']}")
        
        # Early stopping recommendations
        if analysis['early_stopping']['recommendations']:
            report_lines.extend([
                "",
                "EARLY STOPPING RECOMMENDATIONS",
                "-" * 40
            ])
            for rec in analysis['early_stopping']['recommendations']:
                report_lines.append(f"Episode {rec['episode']}: {rec['description']}")
        
        report_lines.append("=" * 80)
        
        report_text = "\n".join(report_lines)
        
        # Save report
        report_file = os.path.join(self.save_dir, f'monitoring_report_ep{self.current_episode}.txt')
        with open(report_file, 'w') as f:
            f.write(report_text)
        
        return report_text
    
    def plot_training_progress(self, save_plots: bool = True) -> Dict[str, str]:
        """Generate training progress plots"""
        if len(self.episode_data) < 5:
            return {}
        
        # Convert to DataFrame
        df = pd.DataFrame(self.episode_data)
        
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(3, 2, figsize=(15, 18))
        
        # 1. Reward curve with smoothing
        episodes = df['episode'].values
        rewards = df['reward'].values
        smoothed_rewards = self.reward_analyzer.get_smoothed_rewards()
        
        axes[0, 0].plot(episodes, rewards, alpha=0.3, color='blue', label='Raw')
        axes[0, 0].plot(episodes, smoothed_rewards, color='red', linewidth=2, label='Smoothed')
        axes[0, 0].set_title('Training Reward Progress')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Reward')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Loss curve
        if 'loss' in df.columns:
            axes[0, 1].plot(df['episode'], df['loss'], color='orange')
            axes[0, 1].set_title('Training Loss')
            axes[0, 1].set_xlabel('Episode')
            axes[0, 1].set_ylabel('Loss')
            axes[0, 1].grid(True, alpha=0.3)
        
        # 3. Exploration rate
        if 'epsilon' in df.columns and df['epsilon'].notna().any():
            axes[1, 0].plot(df['episode'], df['epsilon'], color='green')
            axes[1, 0].set_title('Exploration Rate (ε)')
            axes[1, 0].set_xlabel('Episode')
            axes[1, 0].set_ylabel('Epsilon')
            axes[1, 0].grid(True, alpha=0.3)
        
        # 4. Stability scores
        stability_episodes = []
        stability_rewards = []
        stability_losses = []
        
        for i, episode in enumerate(episodes[::10]):  # Sample every 10 episodes
            if i * 10 < len(episodes):
                stability_episodes.append(episode)
                stability_rewards.append(self.stability_analyzer.calculate_stability_score('reward'))
                stability_losses.append(self.stability_analyzer.calculate_stability_score('loss'))
        
        if stability_episodes:
            axes[1, 1].plot(stability_episodes, stability_rewards, label='Reward Stability', marker='o')
            axes[1, 1].plot(stability_episodes, stability_losses, label='Loss Stability', marker='s')
            axes[1, 1].set_title('Training Stability Scores')
            axes[1, 1].set_xlabel('Episode')
            axes[1, 1].set_ylabel('Stability Score')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        
        # 5. TD-error statistics (if available)
        if self.td_analyzer.td_errors:
            td_stats = self.td_analyzer.get_td_statistics()
            stats_names = ['td_mean', 'td_std', 'td_abs_mean']
            stats_values = [td_stats.get(name, 0) for name in stats_names]
            
            axes[2, 0].bar(stats_names, stats_values)
            axes[2, 0].set_title('TD-Error Statistics')
            axes[2, 0].set_ylabel('Value')
            axes[2, 0].tick_params(axis='x', rotation=45)
        
        # 6. Performance distribution
        axes[2, 1].hist(rewards, bins=30, alpha=0.7, color='purple')
        axes[2, 1].axvline(np.mean(rewards), color='red', linestyle='--', label=f'Mean: {np.mean(rewards):.3f}')
        axes[2, 1].set_title('Reward Distribution')
        axes[2, 1].set_xlabel('Reward')
        axes[2, 1].set_ylabel('Frequency')
        axes[2, 1].legend()
        axes[2, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        plot_paths = {}
        if save_plots:
            plot_file = os.path.join(self.save_dir, f'training_progress_ep{self.current_episode}.png')
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plot_paths['training_progress'] = plot_file
        
        plt.close()
        
        return plot_paths
    
    def save_monitoring_data(self):
        """Save all monitoring data to files"""
        # Save episode data
        episode_file = os.path.join(self.save_dir, 'episode_data.json')
        with open(episode_file, 'w') as f:
            json.dump(self.episode_data, f, indent=2)
        
        # Save analysis
        analysis_file = os.path.join(self.save_dir, 'training_analysis.json')
        analysis = self.get_training_analysis()
        with open(analysis_file, 'w') as f:
            json.dump(analysis, f, indent=2, default=str)
        
        # Generate and save report
        self.generate_monitoring_report()
        
        # Generate and save plots
        self.plot_training_progress()
        
        self.logger.info(f"Monitoring data saved to {self.save_dir}")
    
    def close(self):
        """Close the monitor and clean up resources"""
        if self.enable_tensorboard:
            self.tb_writer.close()
        
        # Save final monitoring data
        self.save_monitoring_data()
        
        self.logger.info("Advanced training monitor closed")


def create_monitor(save_dir: str = None, enable_tensorboard: bool = True) -> AdvancedTrainingMonitor:
    """
    Create an advanced training monitor
    
    Args:
        save_dir: Directory to save monitoring results
        enable_tensorboard: Whether to enable TensorBoard logging
        
    Returns:
        AdvancedTrainingMonitor: Configured monitor instance
    """
    if save_dir is None:
        save_dir = os.path.join('monitoring', datetime.now().strftime('%Y%m%d_%H%M%S'))
    
    return AdvancedTrainingMonitor(
        save_dir=save_dir,
        enable_tensorboard=enable_tensorboard
    )


if __name__ == "__main__":
    # Example usage
    monitor = create_monitor()
    
    # Simulate training episodes
    for episode in range(100):
        reward = np.random.normal(0, 1) + 0.01 * episode  # Improving trend
        loss = 10.0 * np.exp(-0.05 * episode) + np.random.normal(0, 0.1)  # Decreasing loss
        epsilon = max(0.01, 1.0 - 0.01 * episode)  # Decreasing exploration
        
        monitor.log_episode(
            episode=episode,
            reward=reward,
            loss=loss,
            epsilon=epsilon,
            td_error=np.random.normal(0, 0.5),
            value_estimate=reward + np.random.normal(0, 0.1),
            target_value=reward
        )
    
    # Generate final report
    report = monitor.generate_monitoring_report()
    print(report)
    
    monitor.close()
