"""
Hyperparameter Tuning Framework for AlphaPortfolio and General RL Models

This module provides comprehensive hyperparameter optimization capabilities including:
1. Multi-objective optimization (Sharpe ratio, returns, drawdown)
2. Cross-validation with time series splits
3. Early stopping and overfitting detection
4. Support for different RL algorithms
5. Advanced monitoring and logging
"""

import os
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, asdict
import optuna
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner
import torch
import torch.nn as nn
from sklearn.model_selection import TimeSeriesSplit
import matplotlib.pyplot as plt
import seaborn as sns
from concurrent.futures import ProcessPoolExecutor
import pickle
import warnings
warnings.filterwarnings('ignore')

from data_processor import AShareDataProcessor
from models import AlphaPortfolio
from environment import PortfolioEnv
from trainer import PortfolioTrainer
from advanced_monitor import AdvancedTrainingMonitor


@dataclass
class HyperparameterConfig:
    """Hyperparameter configuration class"""
    # Model architecture parameters
    d_model: int = 256
    num_heads: int = 4
    d_ff: int = 1024
    num_layers: int = 2
    dropout: float = 0.1
    
    # Training parameters
    learning_rate: float = 1e-4
    batch_size: int = 64
    gamma: float = 0.99
    eps: float = 1e-8
    weight_decay: float = 0.01
    
    # Environment parameters
    window_size: int = 50
    transaction_cost_pct: float = 0.001
    G: int = 10
    
    # Training control
    num_episodes: int = 100
    eval_frequency: int = 10
    early_stopping_patience: int = 5
    
    # Random seed
    seed: int = 42


@dataclass
class ValidationResult:
    """Validation result container"""
    sharpe_ratio: float
    total_return: float
    max_drawdown: float
    win_rate: float
    volatility: float
    calmar_ratio: float
    sortino_ratio: float
    alpha: float
    beta: float
    information_ratio: float
    
    # Training metrics
    final_loss: float
    convergence_episode: int
    training_stability: float
    
    # Overfitting indicators
    train_val_sharpe_diff: float
    generalization_score: float


class HyperparameterTuner:
    """Advanced hyperparameter tuning framework"""
    
    def __init__(self, 
                 data_processor: AShareDataProcessor,
                 base_config: HyperparameterConfig,
                 study_name: str = None,
                 storage_url: str = None,
                 n_trials: int = 100,
                 timeout: int = 3600,
                 n_jobs: int = 1):
        """
        Initialize the hyperparameter tuner
        
        Args:
            data_processor: Data processor instance
            base_config: Base configuration
            study_name: Optuna study name
            storage_url: Database URL for distributed optimization
            n_trials: Number of optimization trials
            timeout: Timeout in seconds
            n_jobs: Number of parallel jobs
        """
        self.data_processor = data_processor
        self.base_config = base_config
        self.study_name = study_name or f"alphaportfolio_tuning_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.storage_url = storage_url
        self.n_trials = n_trials
        self.timeout = timeout
        self.n_jobs = n_jobs
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        self.setup_logging()
        
        # Results storage
        self.results_dir = os.path.join('tuning_results', self.study_name)
        os.makedirs(self.results_dir, exist_ok=True)
        
        # Initialize monitor
        self.monitor = AdvancedTrainingMonitor(
            save_dir=os.path.join(self.results_dir, 'monitoring'),
            enable_tensorboard=True
        )
        
        # Time series cross-validation
        self.cv_splits = 3
        self.validation_split = 0.2
        
        # Best results tracking
        self.best_results = []
        self.trial_history = []
        
    def setup_logging(self):
        """Setup logging for tuning process"""
        log_file = os.path.join(self.results_dir, 'tuning.log')
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        handler = logging.FileHandler(log_file)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def suggest_hyperparameters(self, trial: optuna.Trial) -> HyperparameterConfig:
        """
        Suggest hyperparameters for a trial
        
        Args:
            trial: Optuna trial object
            
        Returns:
            HyperparameterConfig: Suggested hyperparameters
        """
        config = HyperparameterConfig()
        
        # Model architecture
        config.d_model = trial.suggest_categorical('d_model', [128, 256, 512])
        config.num_heads = trial.suggest_categorical('num_heads', [2, 4, 8])
        config.d_ff = trial.suggest_categorical('d_ff', [512, 1024, 2048])
        config.num_layers = trial.suggest_int('num_layers', 1, 4)
        config.dropout = trial.suggest_float('dropout', 0.0, 0.5)
        
        # Training parameters
        config.learning_rate = trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True)
        config.batch_size = trial.suggest_categorical('batch_size', [32, 64, 128, 256])
        config.gamma = trial.suggest_float('gamma', 0.9, 0.999)
        config.weight_decay = trial.suggest_float('weight_decay', 1e-5, 1e-1, log=True)
        
        # Environment parameters
        config.window_size = trial.suggest_categorical('window_size', [20, 30, 50, 100])
        config.transaction_cost_pct = trial.suggest_float('transaction_cost_pct', 0.0001, 0.01, log=True)
        config.G = trial.suggest_int('G', 5, 20)
        
        # Training control
        config.num_episodes = trial.suggest_int('num_episodes', 50, 500)
        config.eval_frequency = trial.suggest_int('eval_frequency', 5, 20)
        
        # Random seed for reproducibility
        config.seed = trial.suggest_int('seed', 1, 10000)
        
        return config
    
    def prepare_data_splits(self, data: pd.DataFrame) -> List[Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]]:
        """
        Prepare time series cross-validation splits
        
        Args:
            data: Full dataset
            
        Returns:
            List of (train, val, test) splits
        """
        # Sort by date
        data_sorted = data.sort_values('trade_date')
        dates = sorted(data_sorted['trade_date'].unique())
        
        # Create time series splits
        tscv = TimeSeriesSplit(n_splits=self.cv_splits)
        splits = []
        
        for train_idx, test_idx in tscv.split(dates):
            train_dates = [dates[i] for i in train_idx]
            test_dates = [dates[i] for i in test_idx]
            
            # Further split test into validation and test
            val_size = int(len(test_dates) * self.validation_split)
            val_dates = test_dates[:val_size]
            test_dates = test_dates[val_size:]
            
            train_data = data_sorted[data_sorted['trade_date'].isin(train_dates)]
            val_data = data_sorted[data_sorted['trade_date'].isin(val_dates)]
            test_data = data_sorted[data_sorted['trade_date'].isin(test_dates)]
            
            splits.append((train_data, val_data, test_data))
        
        return splits
    
    def evaluate_model(self, 
                      config: HyperparameterConfig, 
                      train_data: pd.DataFrame,
                      val_data: pd.DataFrame,
                      test_data: pd.DataFrame,
                      trial_number: int) -> ValidationResult:
        """
        Evaluate model with given configuration
        
        Args:
            config: Hyperparameter configuration
            train_data: Training data
            val_data: Validation data
            test_data: Test data
            trial_number: Trial number for logging
            
        Returns:
            ValidationResult: Evaluation results
        """
        # Set random seed
        torch.manual_seed(config.seed)
        np.random.seed(config.seed)
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        try:
            # Create environments
            train_env = PortfolioEnv(
                data=train_data,
                window_size=config.window_size,
                transaction_cost_pct=config.transaction_cost_pct
            )
            
            val_env = PortfolioEnv(
                data=val_data,
                window_size=config.window_size,
                transaction_cost_pct=config.transaction_cost_pct
            )
            
            test_env = PortfolioEnv(
                data=test_data,
                window_size=config.window_size,
                transaction_cost_pct=config.transaction_cost_pct
            )
            
            # Create model
            model = AlphaPortfolio(
                input_dim=train_env.num_features,
                d_model=config.d_model,
                num_heads=config.num_heads,
                d_ff=config.d_ff,
                num_layers=config.num_layers,
                dropout=config.dropout,
                G=config.G
            ).to(device)
            
            # Create trainer with monitoring
            trainer = PortfolioTrainer(
                env=train_env,
                model=model,
                device=device,
                learning_rate=config.learning_rate,
                gamma=config.gamma,
                batch_size=config.batch_size,
                monitor=self.monitor
            )
            
            # Train model
            training_results = trainer.train(
                env=train_env,
                num_episodes=config.num_episodes,
                eval_frequency=config.eval_frequency,
                early_stopping_patience=config.early_stopping_patience,
                validation_env=val_env
            )
            
            # Evaluate on validation set
            val_metrics = trainer.evaluate(val_env, num_episodes=10)
            
            # Evaluate on test set
            test_metrics = trainer.evaluate(test_env, num_episodes=10)
            
            # Calculate overfitting indicators
            train_sharpe = training_results['final_train_sharpe']
            val_sharpe = val_metrics['avg_sharpe']
            test_sharpe = test_metrics['avg_sharpe']
            
            train_val_diff = abs(train_sharpe - val_sharpe)
            generalization_score = test_sharpe / max(val_sharpe, 0.01)  # Avoid division by zero
            
            # Create validation result
            result = ValidationResult(
                sharpe_ratio=test_sharpe,
                total_return=test_metrics['total_return'],
                max_drawdown=test_metrics['max_drawdown'],
                win_rate=test_metrics['win_rate'],
                volatility=test_metrics['volatility'],
                calmar_ratio=test_metrics.get('calmar_ratio', 0.0),
                sortino_ratio=test_metrics.get('sortino_ratio', 0.0),
                alpha=test_metrics.get('alpha', 0.0),
                beta=test_metrics.get('beta', 1.0),
                information_ratio=test_metrics.get('information_ratio', 0.0),
                final_loss=training_results['final_loss'],
                convergence_episode=training_results.get('convergence_episode', config.num_episodes),
                training_stability=training_results.get('stability_score', 0.0),
                train_val_sharpe_diff=train_val_diff,
                generalization_score=generalization_score
            )
            
            # Log results
            self.logger.info(f"Trial {trial_number}: Sharpe={test_sharpe:.4f}, "
                           f"Return={test_metrics['total_return']:.4f}, "
                           f"Drawdown={test_metrics['max_drawdown']:.4f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Trial {trial_number} failed: {str(e)}")
            # Return worst possible result
            return ValidationResult(
                sharpe_ratio=-10.0, total_return=-1.0, max_drawdown=-1.0,
                win_rate=0.0, volatility=10.0, calmar_ratio=-10.0,
                sortino_ratio=-10.0, alpha=-1.0, beta=2.0, information_ratio=-10.0,
                final_loss=100.0, convergence_episode=config.num_episodes,
                training_stability=0.0, train_val_sharpe_diff=10.0, generalization_score=0.0
            )
    
    def objective(self, trial: optuna.Trial) -> float:
        """
        Objective function for optimization
        
        Args:
            trial: Optuna trial
            
        Returns:
            float: Objective value (higher is better)
        """
        config = self.suggest_hyperparameters(trial)
        
        # Prepare data
        data = self.data_processor.prepare_training_data(
            start_year=2019, end_year=2023
        )
        
        if data is None:
            return -10.0
        
        # Get cross-validation splits
        splits = self.prepare_data_splits(data)
        
        # Evaluate on all splits
        cv_results = []
        for i, (train_data, val_data, test_data) in enumerate(splits):
            result = self.evaluate_model(config, train_data, val_data, test_data, trial.number)
            cv_results.append(result)
        
        # Calculate cross-validation metrics
        cv_sharpe = np.mean([r.sharpe_ratio for r in cv_results])
        cv_return = np.mean([r.total_return for r in cv_results])
        cv_drawdown = np.mean([r.max_drawdown for r in cv_results])
        cv_generalization = np.mean([r.generalization_score for r in cv_results])
        
        # Multi-objective score with penalties
        base_score = cv_sharpe
        return_bonus = cv_return * 0.1
        drawdown_penalty = abs(cv_drawdown) * 0.5
        generalization_bonus = cv_generalization * 0.2
        
        # Penalize overfitting
        overfitting_penalty = np.mean([r.train_val_sharpe_diff for r in cv_results]) * 0.3
        
        final_score = base_score + return_bonus - drawdown_penalty + generalization_bonus - overfitting_penalty
        
        # Store results
        trial_result = {
            'trial_number': trial.number,
            'config': asdict(config),
            'cv_sharpe': cv_sharpe,
            'cv_return': cv_return,
            'cv_drawdown': cv_drawdown,
            'cv_generalization': cv_generalization,
            'final_score': final_score,
            'individual_results': [asdict(r) for r in cv_results]
        }
        
        self.trial_history.append(trial_result)
        
        # Save intermediate results
        self.save_intermediate_results()
        
        return final_score
    
    def save_intermediate_results(self):
        """Save intermediate results during optimization"""
        results_file = os.path.join(self.results_dir, 'intermediate_results.json')
        with open(results_file, 'w') as f:
            json.dump(self.trial_history, f, indent=2)
    
    def run_optimization(self) -> Dict[str, Any]:
        """
        Run hyperparameter optimization
        
        Returns:
            Dict: Optimization results
        """
        self.logger.info(f"Starting hyperparameter optimization: {self.study_name}")
        self.logger.info(f"Number of trials: {self.n_trials}")
        self.logger.info(f"Timeout: {self.timeout} seconds")
        
        # Create study
        sampler = TPESampler(seed=42)
        pruner = MedianPruner(n_startup_trials=5, n_warmup_steps=10)
        
        study = optuna.create_study(
            study_name=self.study_name,
            storage=self.storage_url,
            load_if_exists=True,
            direction='maximize',
            sampler=sampler,
            pruner=pruner
        )
        
        # Run optimization
        study.optimize(
            self.objective,
            n_trials=self.n_trials,
            timeout=self.timeout,
            n_jobs=self.n_jobs,
            show_progress_bar=True
        )
        
        # Get best results
        best_trial = study.best_trial
        best_config = self.suggest_hyperparameters(best_trial)
        
        # Save results
        results = {
            'best_trial': best_trial.number,
            'best_value': best_trial.value,
            'best_params': best_trial.params,
            'best_config': asdict(best_config),
            'study_summary': {
                'n_trials': len(study.trials),
                'best_value': study.best_value,
                'datetime_start': study.trials[0].datetime_start.isoformat() if study.trials else None,
                'datetime_complete': study.trials[-1].datetime_complete.isoformat() if study.trials else None
            }
        }
        
        # Save to file
        results_file = os.path.join(self.results_dir, 'optimization_results.json')
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        # Generate analysis plots
        self.generate_analysis_plots(study)
        
        self.logger.info(f"Optimization completed. Best value: {study.best_value:.4f}")
        
        return results
    
    def generate_analysis_plots(self, study: optuna.Study):
        """Generate analysis plots for optimization results"""
        try:
            import optuna.visualization as vis
            
            # Optimization history
            fig = vis.plot_optimization_history(study)
            fig.write_html(os.path.join(self.results_dir, 'optimization_history.html'))
            
            # Parameter importance
            fig = vis.plot_param_importances(study)
            fig.write_html(os.path.join(self.results_dir, 'param_importances.html'))
            
            # Parallel coordinate plot
            fig = vis.plot_parallel_coordinate(study)
            fig.write_html(os.path.join(self.results_dir, 'parallel_coordinate.html'))
            
            # Slice plot
            fig = vis.plot_slice(study)
            fig.write_html(os.path.join(self.results_dir, 'slice_plot.html'))
            
        except Exception as e:
            self.logger.warning(f"Failed to generate some plots: {str(e)}")
        
        # Custom analysis plots
        self.generate_custom_plots()
    
    def generate_custom_plots(self):
        """Generate custom analysis plots"""
        if not self.trial_history:
            return
        
        # Convert to DataFrame
        df = pd.DataFrame(self.trial_history)
        
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. Score vs Trial
        axes[0, 0].plot(df['trial_number'], df['final_score'])
        axes[0, 0].set_title('Optimization Progress')
        axes[0, 0].set_xlabel('Trial Number')
        axes[0, 0].set_ylabel('Final Score')
        
        # 2. Sharpe Ratio Distribution
        axes[0, 1].hist(df['cv_sharpe'], bins=20, alpha=0.7)
        axes[0, 1].set_title('Sharpe Ratio Distribution')
        axes[0, 1].set_xlabel('CV Sharpe Ratio')
        axes[0, 1].set_ylabel('Frequency')
        
        # 3. Return vs Drawdown
        axes[0, 2].scatter(df['cv_return'], df['cv_drawdown'], alpha=0.6)
        axes[0, 2].set_title('Return vs Drawdown')
        axes[0, 2].set_xlabel('CV Return')
        axes[0, 2].set_ylabel('CV Max Drawdown')
        
        # 4. Generalization Score
        axes[1, 0].hist(df['cv_generalization'], bins=20, alpha=0.7)
        axes[1, 0].set_title('Generalization Score Distribution')
        axes[1, 0].set_xlabel('Generalization Score')
        axes[1, 0].set_ylabel('Frequency')
        
        # 5. Score Components
        top_10 = df.nlargest(10, 'final_score')
        x = range(len(top_10))
        axes[1, 1].bar(x, top_10['cv_sharpe'], alpha=0.7, label='Sharpe')
        axes[1, 1].bar(x, top_10['cv_return'], alpha=0.7, label='Return')
        axes[1, 1].set_title('Top 10 Trials - Score Components')
        axes[1, 1].set_xlabel('Trial Rank')
        axes[1, 1].legend()
        
        # 6. Convergence Analysis
        rolling_best = df['final_score'].cummax()
        axes[1, 2].plot(df['trial_number'], rolling_best)
        axes[1, 2].set_title('Best Score Convergence')
        axes[1, 2].set_xlabel('Trial Number')
        axes[1, 2].set_ylabel('Best Score So Far')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.results_dir, 'custom_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def get_best_config(self) -> HyperparameterConfig:
        """Get the best configuration from optimization results"""
        results_file = os.path.join(self.results_dir, 'optimization_results.json')
        if os.path.exists(results_file):
            with open(results_file, 'r') as f:
                results = json.load(f)
            return HyperparameterConfig(**results['best_config'])
        else:
            return self.base_config
    
    def train_final_model(self, config: HyperparameterConfig = None) -> str:
        """
        Train final model with best configuration
        
        Args:
            config: Configuration to use (if None, uses best from optimization)
            
        Returns:
            str: Path to saved model
        """
        if config is None:
            config = self.get_best_config()
        
        self.logger.info("Training final model with best configuration")
        
        # Prepare full dataset
        data = self.data_processor.prepare_training_data(
            start_year=2019, end_year=2023
        )
        
        # Set random seed
        torch.manual_seed(config.seed)
        np.random.seed(config.seed)
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Create environment
        env = PortfolioEnv(
            data=data,
            window_size=config.window_size,
            transaction_cost_pct=config.transaction_cost_pct
        )
        
        # Create model
        model = AlphaPortfolio(
            input_dim=env.num_features,
            d_model=config.d_model,
            num_heads=config.num_heads,
            d_ff=config.d_ff,
            num_layers=config.num_layers,
            dropout=config.dropout,
            G=config.G
        ).to(device)
        
        # Create trainer
        trainer = PortfolioTrainer(
            env=env,
            model=model,
            device=device,
            learning_rate=config.learning_rate,
            gamma=config.gamma,
            batch_size=config.batch_size,
            monitor=self.monitor
        )
        
        # Train model
        trainer.train(
            env=env,
            num_episodes=config.num_episodes * 2,  # Train longer for final model
            eval_frequency=config.eval_frequency,
            early_stopping_patience=config.early_stopping_patience * 2
        )
        
        # Save model
        model_path = os.path.join(self.results_dir, 'best_model.pth')
        trainer.save_model(model_path)
        
        # Save configuration
        config_path = os.path.join(self.results_dir, 'best_config.json')
        with open(config_path, 'w') as f:
            json.dump(asdict(config), f, indent=2)
        
        self.logger.info(f"Final model saved to: {model_path}")
        
        return model_path


def create_tuning_study(data_processor: AShareDataProcessor,
                       study_name: str = None,
                       n_trials: int = 100) -> HyperparameterTuner:
    """
    Create a hyperparameter tuning study
    
    Args:
        data_processor: Data processor instance
        study_name: Name for the study
        n_trials: Number of trials to run
        
    Returns:
        HyperparameterTuner: Configured tuner instance
    """
    base_config = HyperparameterConfig()
    
    tuner = HyperparameterTuner(
        data_processor=data_processor,
        base_config=base_config,
        study_name=study_name,
        n_trials=n_trials
    )
    
    return tuner


if __name__ == "__main__":
    # Example usage
    from data_processor import AShareDataProcessor
    
    # Create data processor
    processor = AShareDataProcessor(index_type='hs300', top_stocks=50)
    
    # Create tuner
    tuner = create_tuning_study(
        data_processor=processor,
        study_name="alphaportfolio_optimization_demo",
        n_trials=50
    )
    
    # Run optimization
    results = tuner.run_optimization()
    
    # Train final model
    model_path = tuner.train_final_model()
    
    print(f"Optimization completed!")
    print(f"Best score: {results['best_value']:.4f}")
    print(f"Final model saved to: {model_path}")
