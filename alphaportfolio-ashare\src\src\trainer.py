import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import ReduceLROnPlateau
import numpy as np
from collections import deque
import random
import os
import quantstats as qs
import logging
from typing import List, Tuple, Dict, Any
from tqdm import tqdm  # 导入tqdm
import pandas as pd
import time
from datetime import datetime

class ReplayBuffer:
    """经验回放缓冲区"""
    
    def __init__(self, capacity: int):
        """
        初始化缓冲区
        
        Args:
            capacity: 缓冲区容量
        """
        self.buffer = deque(maxlen=capacity)
    
    def store(self, transition):
        """
        存储转换
        
        Args:
            transition: (状态, 动作, 奖励, 下一状态, 结束标志)
        """
        self.buffer.append(transition)
    
    def sample(self, batch_size: int) -> List[Tuple]:
        """
        随机采样
        
        Args:
            batch_size: 批次大小
            
        Returns:
            List: 采样的转换
        """
        return random.sample(self.buffer, batch_size)
    
    def __len__(self) -> int:
        """
        获取缓冲区长度
        
        Returns:
            int: 缓冲区长度
        """
        return len(self.buffer)

class SafeReplayBuffer(ReplayBuffer):
    """安全的经验回放缓冲区，可以处理NaN值"""
    
    def __init__(self, capacity: int, logger=None):
        """
        初始化安全缓冲区
        
        Args:
            capacity: 缓冲区容量
            logger: 日志记录器
        """
        super().__init__(capacity)
        self.nan_count = 0
        self.logger = logger
        
    def sample(self, batch_size: int, max_attempts: int = 5) -> Tuple:
        """
        安全随机采样，检测并处理NaN值
        
        Args:
            batch_size: 批次大小
            max_attempts: 最大尝试次数，如果连续max_attempts次采样都有NaN值，则返回清理后的样本
            
        Returns:
            Tuple: (states, actions, rewards, next_states, dones)
        """
        # 如果缓冲区样本不足，则减小批次大小
        if len(self.buffer) < batch_size:
            if self.logger:
                self.logger.warning(f"缓冲区样本不足: {len(self.buffer)} < {batch_size}，减小批次大小")
            batch_size = len(self.buffer)
            
        # 采样并转换为numpy数组
        batch = random.sample(self.buffer, batch_size)
        states, actions, rewards, next_states, dones = zip(*batch)
        
        # 转换为numpy数组
        states = np.array(states, dtype=np.float32)
        if isinstance(actions[0], np.ndarray):
            actions = np.array(actions, dtype=np.float32)
        else:
            actions = np.array(actions)
        rewards = np.array(rewards, dtype=np.float32)
        next_states = np.array(next_states, dtype=np.float32)
        dones = np.array(dones, dtype=np.float32)
        
        # 检查是否包含NaN值
        has_nan = (
            np.isnan(states).any() or 
            np.isnan(actions).any() or 
            np.isnan(rewards).any() or 
            np.isnan(next_states).any()
        )
        
        # 如果有NaN值，记录并尝试清理
        if has_nan:
            self.nan_count += 1
            if self.logger:
                self.logger.warning(f"采样数据包含NaN值 (第{self.nan_count}次)")
            
            # 清理NaN值
            states = np.nan_to_num(states, nan=0.0)
            if isinstance(actions[0], np.ndarray):
                actions = np.nan_to_num(actions, nan=0.0)
            rewards = np.nan_to_num(rewards, nan=0.0)
            next_states = np.nan_to_num(next_states, nan=0.0)
            
            if self.logger:
                self.logger.info("已将NaN值替换为0")
        
        return states, actions, rewards, next_states, dones
    
    def store(self, transition):
        """
        安全存储转换，检查是否包含NaN值
        
        Args:
            transition: (状态, 动作, 奖励, 下一状态, 结束标志)
            
        Returns:
            bool: 是否成功存储
        """
        state, action, reward, next_state, done = transition
        
        # 检查是否包含NaN值
        if (
            (isinstance(state, np.ndarray) and np.isnan(state).any()) or
            (isinstance(action, np.ndarray) and np.isnan(action).any()) or
            np.isnan(reward).any() if isinstance(reward, np.ndarray) else np.isnan(reward) or
            (isinstance(next_state, np.ndarray) and np.isnan(next_state).any())
        ):
            self.nan_count += 1
            if self.logger:
                self.logger.warning(f"存储的数据包含NaN值 (第{self.nan_count}次)")
            return False
        
        # 存储转换
        super().store(transition)
        return True

class Trainer:
    """基础训练器类"""
    
    def __init__(
        self, 
        env, 
        model, 
        target_model=None,
        learning_rate: float = 0.0001,
        gamma: float = 0.99,
        buffer_size: int = 10000,
        batch_size: int = 32,
        target_update: int = 10,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        初始化训练器
        
        Args:
            env: 环境
            model: 策略网络
            target_model: 目标网络，可选
            learning_rate: 学习率
            gamma: 折扣因子
            buffer_size: 经验回放缓冲区大小
            batch_size: 批次大小
            target_update: 目标网络更新频率
            device: 计算设备，默认使用GPU如果可用
        """
        self.env = env
        self.model = model
        self.target_model = target_model
        self.gamma = gamma
        self.batch_size = batch_size
        self.target_update = target_update
        self.device = device
        self.buffer = ReplayBuffer(buffer_size)
        
        # 使用AdamW优化器 - 比Adam更好的权重衰减实现
        self.optimizer = optim.AdamW(
            self.model.parameters(), 
            lr=learning_rate,
            weight_decay=1e-4  # 添加权重衰减防止过拟合
        )
        
        # 添加学习率调度器 - 根据验证损失自动调整学习率
        self.scheduler = ReduceLROnPlateau(
            self.optimizer, 
            mode='min',  # 监视损失最小化
            factor=0.5,  # 每次降低学习率的因子
            patience=5,   # 等待多少轮次无改善后降低学习率
            verbose=True, # 打印学习率变化
            min_lr=1e-6   # 最小学习率
        )
        
        # 训练统计
        self.train_count = 0
        self.nan_count = 0  # 跟踪NaN出现次数
        self.max_nan_tolerance = 5  # 最大NaN容忍次数
        
        # 设置损失函数
        self.criterion = nn.MSELoss()
        
        # 设置梯度裁剪阈值
        self.grad_clip_value = 1.0
        
        # 配置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        # 不再使用logging.basicConfig，而是获取已配置的日志记录器
        self.logger = logging.getLogger("Trainer")
        # 添加日志记录
        self.logger.info("初始化训练器")
    
    def update_target_model(self):
        """更新目标网络"""
        self.target_model.load_state_dict(self.model.state_dict())
        
    def select_action(self, state, epsilon=0.1):
        """
        根据epsilon-greedy策略选择动作
        
        Args:
            state: 当前状态
            epsilon: 探索率
            
        Returns:
            action: 选择的动作索引
            weights: 权重向量
        """
        # 以epsilon的概率随机选择动作（探索）
        if random.random() < epsilon:
            # 创建随机权重向量
            num_assets = self.env.action_space.shape[0]
            weights = torch.softmax(torch.randn(num_assets), dim=0).to(self.device)
            action = weights.argmax().item()
            return action, weights
        
        # 否则使用模型选择最优动作（利用）
        with torch.no_grad():
            self.model.eval()
            state_tensor = state.unsqueeze(0)  # 添加批次维度
            model_output = self.model(state_tensor)
            
            # 处理模型输出，可能是元组 (weights, attention_weights)
            if isinstance(model_output, tuple):
                weights = model_output[0].squeeze(0)  # 只取权重部分
            else:
                weights = model_output.squeeze(0)
            
            # 确保权重非负并且和为1
            weights = torch.softmax(weights, dim=0)
            
            # 选择权重最大的资产作为动作
            action = weights.argmax().item()
            
            self.model.train()
            
        return action, weights
    
    def store_transition(self, state, weights, reward, next_state, done):
        """
        存储转换到缓冲区
        
        Args:
            state: 当前状态
            weights: 当前权重
            reward: 获得的奖励
            next_state: 下一状态
            done: 是否结束
            
        Returns:
            bool: 是否成功存储
        """
        # 检查输入是否包含NaN
        if (np.isnan(reward) or 
            (isinstance(state, np.ndarray) and np.isnan(state).any()) or 
            (isinstance(next_state, np.ndarray) and np.isnan(next_state).any()) or
            (isinstance(weights, np.ndarray) and np.isnan(weights).any())):
            
            self.logger.warning("转换中包含NaN值，已跳过存储")
            self.nan_count += 1
                
            return False
            
        # 确保所有数据都是简单的NumPy数组
        if isinstance(state, torch.Tensor):
            state = state.detach().cpu().numpy()
        
        if isinstance(weights, torch.Tensor):
            # 确保weights是一维数组
            if len(weights.shape) > 1:
                # 如果是多维张量，只保留最后一个维度
                weights = weights.reshape(-1).detach().cpu().numpy()
            else:
                weights = weights.detach().cpu().numpy()
        
        if isinstance(next_state, torch.Tensor):
            next_state = next_state.detach().cpu().numpy()
        
        # 确保reward是标量
        if isinstance(reward, torch.Tensor):
            reward = reward.item()
        
        # 再次检查转换后的数据是否包含NaN
        if (np.isnan(reward) or 
            np.isnan(state).any() or 
            np.isnan(next_state).any() or
            np.isnan(weights).any()):
            
            self.logger.warning("转换为NumPy后的数据包含NaN值")
            return False
        
        self.buffer.store((state, weights, reward, next_state, done))
        return True
    
    def train_step(self):
        """
        执行一步训练
        
        Returns:
            float: 损失值
        """
        # 如果缓冲区中的样本不足，则跳过训练
        if len(self.buffer) < self.batch_size:
            return 0.0
            
        self.train_count += 1
            
        # 从缓冲区中采样
        transitions = self.buffer.sample(self.batch_size)
        states, actions, rewards, next_states, dones = zip(*transitions)
        
        # 转换为张量
        states = torch.FloatTensor(np.array(states)).to(self.device)
        actions = torch.LongTensor(actions).to(self.device)
        rewards = torch.FloatTensor(rewards).to(self.device)
        next_states = torch.FloatTensor(np.array(next_states)).to(self.device)
        dones = torch.FloatTensor(dones).to(self.device)
        
        # 检查输入张量中是否有NaN
        if (torch.isnan(states).any() or torch.isnan(rewards).any() or 
            torch.isnan(next_states).any()):
            self.logger.warning("训练批次中包含NaN值")
            self.nan_count += 1
            
            # 如果NaN出现过多，停止训练
            if self.nan_count > self.max_nan_tolerance:
                self.logger.error(f"NaN值出现次数超过阈值({self.max_nan_tolerance})，停止训练")
                return float('nan')
                
            states = torch.nan_to_num(states, nan=0.0)
            rewards = torch.nan_to_num(rewards, nan=0.0)
            next_states = torch.nan_to_num(next_states, nan=0.0)
        
        try:
            # 计算当前Q值
            current_q_values = self.model(states).gather(1, actions.unsqueeze(1)).squeeze(1)
            
            # 计算目标Q值
            with torch.no_grad():
                target_q_values = self.target_model(next_states).max(1)[0]
                target_q_values = rewards + (1 - dones) * self.gamma * target_q_values
                
            # 检查计算结果中是否有NaN
            if torch.isnan(current_q_values).any() or torch.isnan(target_q_values).any():
                self.logger.warning("Q值计算结果中存在NaN")
                self.nan_count += 1
                return float('nan')
            
            # 计算损失
            loss = self.criterion(current_q_values, target_q_values)
            
            # 清零梯度
            self.optimizer.zero_grad()
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪，防止梯度爆炸
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.grad_clip_value)
            
            # 更新参数
            self.optimizer.step()
            
            # 更新学习率
            self.scheduler.step(loss)
            
            # 检查更新后的模型是否有NaN权重
            has_nan_weights = False
            for name, param in self.model.named_parameters():
                if torch.isnan(param.data).any():
                    has_nan_weights = True
                    self.logger.warning(f"模型权重 {name} 中存在NaN")
                    
            if has_nan_weights:
                self.nan_count += 1
                # 如果有NaN权重，恢复到目标模型（应该是稳定的）
                self.model.load_state_dict(self.target_model.state_dict())
                self.logger.warning("由于NaN权重，已将模型重置为目标模型")
                
            # 如果到达目标更新频率，更新目标网络
            if self.train_count % self.target_update == 0:
                self.update_target_model()
                
            return loss.item()
            
        except Exception as e:
            self.logger.error(f"训练步骤出错: {str(e)}")
            self.nan_count += 1
            return float('nan')
    
    # def train_episode(self, epsilon=0.1):
    #     """
    #     训练一个完整的回合
    #
    #     Args:
    #         epsilon: 探索率
    #
    #     Returns:
    #         episode_reward: 回合总奖励
    #         avg_loss: 平均损失
    #         steps: 回合步数
    #         nan_detected: 是否检测到NaN值
    #     """
    #     state = self.env.reset()
    #     done = False
    #     episode_reward = 0
    #     losses = []
    #     steps = 0
    #     nan_detected = False
    #
    #     while not done and not nan_detected:
    #         # 选择动作
    #         action, weights = self.select_action(state, epsilon)
    #
    #         # 检查权重中是否有NaN
    #         if torch.isnan(weights).any():
    #             self.logger.warning("检测到NaN权重，使用均匀分布")
    #             weights = torch.ones_like(weights) / weights.size(0)
    #             nan_detected = True
    #
    #         # 执行动作
    #         next_state, reward, done, _ = self.env.step(weights.cpu().numpy())
    #
    #         # 存储转换
    #         self.store_transition(state, weights, reward, next_state, done)
    #
    #         # 如果有足够样本，进行学习
    #         if len(self.buffer) > self.batch_size:
    #             loss = self.train_step()
    #
    #             # 检查损失是否为NaN
    #             if np.isnan(loss):
    #                 self.logger.warning("检测到NaN损失，终止训练回合")
    #                 nan_detected = True
    #                 break
    #
    #             losses.append(loss)
    #
    #         state = next_state
    #         episode_reward += reward
    #         steps += 1
    #
    #         # 更新目标网络
    #         if steps % self.target_update == 0:
    #             self.update_target_model()
    #
    #     avg_loss = np.mean(losses) if losses else 0
    #     return episode_reward, avg_loss, steps, nan_detected
    
    # def evaluate(self, num_episodes: int = 5) -> Dict[str, Any]:
    #     """
    #     评估模型性能
    #
    #     Args:
    #         num_episodes: 评估回合数
    #
    #     Returns:
    #         Dict[str, Any]: 评估指标
    #     """
    #     self.model.eval()
    #     if self.target_model is not None:
    #         self.target_model.eval()
    #
    #     episode_rewards = []
    #     episode_portfolio_values = []
    #     episode_sharpe_ratios = []
    #     episode_max_drawdowns = []
    #     episode_win_rates = []
    #
    #     for episode in range(num_episodes):
    #         try:
    #             state = self.env.reset()
    #             done = False
    #             episode_reward = 0
    #             portfolio_values = []
    #
    #             while not done:
    #                 # 确保状态是PyTorch张量
    #                 if isinstance(state, np.ndarray):
    #                     state_tensor = torch.FloatTensor(state).to(self.device)
    #                 else:
    #                     state_tensor = state.to(self.device)
    #
    #                 # 选择动作
    #                 with torch.no_grad():
    #                     weights, _ = self.model(state_tensor)
    #                     action = weights.cpu().numpy()[0]
    #
    #                 # 执行动作
    #                 next_state, reward, done, info = self.env.step(action)
    #                 episode_reward += reward
    #                 portfolio_values.append(info['portfolio_value'])
    #                 state = next_state
    #
    #             # 计算夏普比率
    #             returns = np.diff(portfolio_values) / portfolio_values[:-1]
    #             sharpe_ratio = np.mean(returns) / (np.std(returns) + 1e-6) * np.sqrt(252)
    #
    #             # 计算最大回撤
    #             portfolio_values = np.array(portfolio_values)
    #             peak = np.maximum.accumulate(portfolio_values)
    #             drawdown = (peak - portfolio_values) / peak
    #             max_drawdown = np.max(drawdown)
    #
    #             # 计算胜率
    #             win_rate = np.mean(returns > 0)
    #
    #             # 记录指标
    #             episode_rewards.append(episode_reward)
    #             episode_portfolio_values.append(portfolio_values[-1])
    #             episode_sharpe_ratios.append(sharpe_ratio)
    #             episode_max_drawdowns.append(max_drawdown)
    #             episode_win_rates.append(win_rate)
    #
    #             self.logger.info(
    #                 f"评估回合 {episode+1}/{num_episodes}: "
    #                 f"奖励={episode_reward:.2f}, "
    #                 f"夏普比率={sharpe_ratio:.2f}, "
    #                 f"最大回撤={max_drawdown:.2%}, "
    #                 f"胜率={win_rate:.2%}"
    #             )
    #
    #         except Exception as e:
    #             self.logger.error(f"评估回合 {episode+1} 出错: {str(e)}")
    #             import traceback
    #             self.logger.error(traceback.format_exc())
    #             continue
    #
    #     # 计算平均指标
    #     metrics = {
    #         'mean_reward': np.mean(episode_rewards),
    #         'std_reward': np.std(episode_rewards),
    #         'mean_portfolio_value': np.mean(episode_portfolio_values),
    #         'std_portfolio_value': np.std(episode_portfolio_values),
    #         'mean_sharpe': np.mean(episode_sharpe_ratios),
    #         'std_sharpe': np.std(episode_sharpe_ratios),
    #         'mean_max_drawdown': np.mean(episode_max_drawdowns),
    #         'std_max_drawdown': np.std(episode_max_drawdowns),
    #         'mean_win_rate': np.mean(episode_win_rates),
    #         'std_win_rate': np.std(episode_win_rates)
    #     }
    #
    #     self.model.train()
    #     if self.target_model is not None:
    #         self.target_model.train()
    #
    #     return metrics
    def evaluate_new(self, num_episodes: int = 5, risk_free_rate: float = 0.02,
                     benchmark_path: str = '/tmp/pycharm_project_525/alphaportfolio-ashare/dataset/benchmark_returns.csv') -> List[Dict[str, Any]]:
        """
        多回合评估模型性能（返回每个 episode 的指标）

        Args:
            num_episodes: 评估回合数
            risk_free_rate: 年无风险利率
            benchmark_path: 可选，benchmark CSV 文件路径，包含 ['trade_date', 'returns']

        Returns:
            List[Dict[str, Any]]: 每个 episode 的完整评估指标
        """
        self.model.eval()
        if self.target_model is not None:
            self.target_model.eval()

        #episode_metrics_list = []

        for episode in range(num_episodes):
            try:
                state = self.env.reset()
                done = False
                episode_reward = 0
                portfolio_values = []

                while not done:
                    state_tensor = torch.FloatTensor(state).to(self.device) if isinstance(state,
                                                                                          np.ndarray) else state.to(
                        self.device)
                    with torch.no_grad():
                        weights, _ = self.model(state_tensor)
                        action = weights.cpu().numpy()[0]
                    next_state, reward, done, info = self.env.step(action)
                    episode_reward += reward
                    portfolio_values.append(info['portfolio_value'])
                    state = next_state

                portfolio_values = np.array(portfolio_values)
                if len(portfolio_values) >= 2:
                    daily_ret = (portfolio_values[1:] - portfolio_values[:-1]) / (portfolio_values[:-1] + 1e-8)
                    date_index = pd.date_range(start="2000-01-01", periods=len(daily_ret), freq='D')
                    returns_series = pd.Series(daily_ret, index=date_index)

                    ARR = qs.stats.cagr(returns_series, periods=365)
                    AVol = qs.stats.volatility(returns_series, annualize=True)
                    MDD = qs.stats.max_drawdown(returns_series)
                    ASR = qs.stats.sharpe(returns_series, rf=risk_free_rate)
                    CR = qs.stats.calmar(returns_series)

                    IR = 0.0
                    if benchmark_path is not None:
                        try:
                            benchmark_df = pd.read_csv(benchmark_path, parse_dates=["trade_date"])
                            benchmark_returns = pd.Series(data=benchmark_df["returns"].values,
                                                          index=benchmark_df["trade_date"])
                            benchmark_returns = benchmark_returns[:len(returns_series)]
                            IR = qs.stats.information_ratio(returns_series, benchmark_returns)
                        except Exception as e:
                            self.logger.warning(f"[IR计算失败] benchmark 加载失败: {e}")
                            IR = 0.0

                    win_rate = np.mean(daily_ret > 0)
                else:
                    ARR = AVol = MDD = ASR = CR = IR = win_rate = 0.0

                result = {
                    'episode': episode + 1,
                    'reward': episode_reward,
                    'final_portfolio_value': portfolio_values[-1] if len(portfolio_values) > 0 else 0.0,
                    'ARR': ARR,
                    'AVol': AVol,
                    'MDD': MDD,
                    'ASR': ASR,
                    'CR': CR,
                    'IR': IR,
                    'win_rate': win_rate
                }
                #episode_metrics_list.append(result)

                self.logger.info(
                    f"[回合 {episode + 1}/{num_episodes}] "
                    f"Reward={episode_reward:.6f}, ARR={ARR:.6f}, AVol={AVol:.6f}, "
                    f"MDD={MDD:.6f}, ASR={ASR:.6f}, CR={CR:.6f}, IR={IR:.6f}, WinRate={win_rate:.6f}"
                )

            except Exception as e:
                self.logger.error(f"评估回合 {episode + 1} 出错: {str(e)}")
                import traceback
                self.logger.error(traceback.format_exc())
                continue

        self.model.train()
        if self.target_model is not None:
            self.target_model.train()

        return result

    def save_model(self, path: str, timestamp: str = None):
        """
        保存模型
        
        Args:
            path: 保存路径
            timestamp: 统一的时间戳
        """
        if timestamp is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
        # 确保目录存在
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # 保存模型状态
        model_state = {
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if hasattr(self, 'scheduler') else None,
            'episode': self.current_episode if hasattr(self, 'current_episode') else 0,
            'best_reward': self.best_reward if hasattr(self, 'best_reward') else float('-inf'),
            'timestamp': timestamp
        }
        
        # 如果有目标模型，也保存其状态
        if self.target_model is not None:
            model_state['target_model_state_dict'] = self.target_model.state_dict()
            
        # 保存模型
        torch.save(model_state, path)
        
        # 记录保存信息
        if hasattr(self, 'logger'):
            self.logger.info(f"模型已保存到: {path}")
            self.logger.info(f"当前回合: {model_state['episode']}")
            self.logger.info(f"最佳奖励: {model_state['best_reward']}")
            self.logger.info(f"时间戳: {timestamp}")
    
    def load_model(self, path: str):
        """
        加载模型
        
        Args:
            path: 加载路径
        """
        try:
            checkpoint = torch.load(path)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            
            # 如果有target_model且checkpoint中包含其状态
            if self.target_model is not None and 'target_model_state_dict' in checkpoint:
                self.target_model.load_state_dict(checkpoint['target_model_state_dict'])
                
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            
            # 如果有调度器状态，加载它
            if hasattr(self, 'scheduler') and self.scheduler is not None and 'scheduler_state_dict' in checkpoint:
                self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
                
            self.logger.info(f"模型从 {path} 加载成功")
        except Exception as e:
            self.logger.error(f"加载模型出错: {str(e)}")

class PortfolioTrainer(Trainer):
    """
    用于训练强化学习投资组合管理模型的训练器
    """
    
    def __init__(
        self, 
        env, 
        model, 
        target_model=None, 
        gamma=0.99, 
        learning_rate=1e-4, 
        buffer_size=100000, 
        batch_size=64, 
        target_update=64,
        double_dqn=False,
        eps_start=1.0,
        eps_end=0.01,
        eps_decay=0.998,
        device='cpu',
        clip_grad_norm=1.0,
        patience=10,
        factor=0.7,
        min_lr=1e-6,
        warm_up_steps=5000,
        max_nan_tolerance=10,
        timestamp=None  # 添加时间戳参数
    ):
        """
        初始化PortfolioTrainer
        
        Args:
            env: 投资环境
            model: 策略模型
            target_model: 目标模型，用于稳定训练
            gamma: 折扣因子
            learning_rate: 学习率
            buffer_size: 经验回放缓冲区大小
            batch_size: 批量大小
            target_update: 目标模型更新频率
            double_dqn: 是否使用Double DQN
            eps_start: 初始探索率
            eps_end: 最小探索率
            eps_decay: 探索率衰减因子
            device: 计算设备
            clip_grad_norm: 梯度裁剪范数
            patience: 学习率调度器容忍度
            factor: 学习率调度器衰减因子
            min_lr: 最小学习率
            warm_up_steps: 学习率热身步数
            max_nan_tolerance: 最大NaN容忍次数，超过此值将停止训练
            timestamp: 统一的时间戳
        """
        # 正确地调用父类初始化方法，传递所有必要的参数
        super().__init__(
            env=env, 
            model=model, 
            target_model=target_model,
            learning_rate=learning_rate,
            gamma=gamma,
            buffer_size=buffer_size,
            batch_size=batch_size,
            target_update=target_update,
            device=device
        )
        
        self.double_dqn = double_dqn
        self.clip_grad_norm = clip_grad_norm
        self.learning_rate = learning_rate  # 保存学习率为实例属性
        
        # 探索参数
        self.eps_start = eps_start
        self.eps_end = eps_end
        self.eps_decay = eps_decay
        
        # 学习率调度器参数
        self.patience = patience
        self.factor = factor
        self.min_lr = min_lr
        self.warm_up_steps = warm_up_steps
        
        # 训练计数器
        self.train_count = 0
        self.current_step = 0
        self.nan_detected_count = 0  # 添加NaN检测计数器
        self.max_nan_tolerance = max_nan_tolerance
        
        # 设置经验回放缓冲区
        self.logger.info("使用SafeReplayBuffer替代标准ReplayBuffer以提高数值稳定性")
        self.buffer = SafeReplayBuffer(buffer_size, logger=self.logger)
        
        # 设置优化器
        self.optimizer = torch.optim.AdamW(  # 使用AdamW替代Adam，有更好的权重衰减
            self.model.parameters(), 
            lr=learning_rate,
            weight_decay=1e-4,  # 添加权重衰减以减少过拟合
            eps=1e-5  # 增大epsilon参数以提高数值稳定性
        )
        
        # 设置学习率调度器
        self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, 
            mode='max', 
            factor=factor, 
            patience=patience,
            cooldown=5,
            min_lr=min_lr,
            verbose=True
        )
        
        # 损失函数
        self.criterion = nn.SmoothL1Loss()  # 使用SmoothL1Loss替代MSELoss，对异常值更加鲁棒
        
        # 如果提供了目标模型，则同步初始参数
        if self.target_model is not None:
            self.update_target_model()
        
        self.timestamp = timestamp or datetime.now().strftime('%Y%m%d_%H%M%S')

        # 初始化其他属性
        self.current_episode = 0
        self.best_reward = float('-inf')
        self.nan_count = 0
        self.episode_rewards = []
        self.episode_losses = []
        self.episode_sharpe_ratios = []
        self.epsilon_values = []
    
    def should_early_stop(self, nan_detected):
        """
        检查是否应该提前停止训练
        
        Args:
            nan_detected: 本次迭代是否检测到NaN值
            
        Returns:
            bool: 是否应该提前停止训练
        """
        if nan_detected:
            self.nan_detected_count += 1
            self.logger.warning(f"检测到NaN值（第{self.nan_detected_count}次，容忍度:{self.max_nan_tolerance}）")
            
            # 如果连续检测到过多NaN值，则停止训练
            if self.nan_detected_count >= self.max_nan_tolerance:
                self.logger.error(f"连续检测到{self.nan_detected_count}次NaN值，超过容忍度{self.max_nan_tolerance}，停止训练")
                return True
        else:
            # 如果本次没有检测到NaN，将计数器减1
            self.nan_detected_count = max(0, self.nan_detected_count - 1)
            
        return False
    
    def update_target_model(self):
        """
        更新目标模型的参数为当前模型的参数
        """
        if self.target_model is not None:
            self.target_model.load_state_dict(self.model.state_dict())
        
    def select_action(self, state, epsilon=0.1):
        """
        根据epsilon-greedy策略选择动作
        
        Args:
            state: 当前状态
            epsilon: 探索率
            
        Returns:
            action: 选择的动作索引
            weights: 权重向量
        """
        # 以epsilon的概率随机选择动作（探索）
        if random.random() < epsilon:
            # 创建随机权重向量
            num_assets = self.env.action_space.shape[0]
            weights = torch.softmax(torch.randn(num_assets), dim=0).to(self.device)
            action = weights.argmax().item()
            return action, weights
        
        # 否则使用模型选择最优动作（利用）
        with torch.no_grad():
            self.model.eval()
            state_tensor = state.unsqueeze(0)  # 添加批次维度
            model_output = self.model(state_tensor)
            
            # 处理模型输出，可能是元组 (weights, attention_weights)
            if isinstance(model_output, tuple):
                weights = model_output[0].squeeze(0)  # 只取权重部分
            else:
                weights = model_output.squeeze(0)
            
            # 确保权重非负并且和为1
            weights = torch.softmax(weights, dim=0)
            
            # 选择权重最大的资产作为动作
            action = weights.argmax().item()
            
            self.model.train()
            
        return action, weights
    
    def store_transition(self, state, weights, reward, next_state, done):
        """
        存储转换到缓冲区
        
        Args:
            state: 当前状态
            weights: 当前权重
            reward: 获得的奖励
            next_state: 下一状态
            done: 是否结束
            
        Returns:
            bool: 是否成功存储
        """
        # 检查输入是否包含NaN
        if (np.isnan(reward) or 
            (isinstance(state, np.ndarray) and np.isnan(state).any()) or 
            (isinstance(next_state, np.ndarray) and np.isnan(next_state).any()) or
            (isinstance(weights, np.ndarray) and np.isnan(weights).any())):
            
            self.logger.warning("转换中包含NaN值，已跳过存储")
            self.nan_count += 1
                
            return False
            
        # 确保所有数据都是简单的NumPy数组
        if isinstance(state, torch.Tensor):
            state = state.detach().cpu().numpy()
        
        if isinstance(weights, torch.Tensor):
            # 确保weights是一维数组
            if len(weights.shape) > 1:
                # 如果是多维张量，只保留最后一个维度
                weights = weights.reshape(-1).detach().cpu().numpy()
            else:
                weights = weights.detach().cpu().numpy()
        
        if isinstance(next_state, torch.Tensor):
            next_state = next_state.detach().cpu().numpy()
        
        # 确保reward是标量
        if isinstance(reward, torch.Tensor):
            reward = reward.item()
        
        # 再次检查转换后的数据是否包含NaN
        if (np.isnan(reward) or 
            np.isnan(state).any() or 
            np.isnan(next_state).any() or
            np.isnan(weights).any()):
            
            self.logger.warning("转换为NumPy后的数据包含NaN值")
            return False
        
        self.buffer.store((state, weights, reward, next_state, done))
        return True
    
    def train_episode(self, epsilon=None, render=False):
        """
        训练单个回合
        
        Args:
            epsilon: 探索率，如果为None则使用当前的self.eps
            render: 是否渲染环境
            
        Returns:
            episode_reward: 回合总奖励
            avg_loss: 平均损失
            steps: 回合步数
            nan_detected: 是否检测到NaN值
        """
        # 初始化回合状态
        done = False
        episode_reward = 0
        steps = 0
        losses = []
        nan_detected = False
        
        # 获取初始状态
        state = self.env.reset()
        
        # 将状态转为张量并移至设备
        if not isinstance(state, torch.Tensor):
            state = torch.FloatTensor(state).to(self.device)
        
        # 检查状态是否包含NaN
        if torch.isnan(state).any():
            self.logger.warning("初始状态包含NaN值，已替换为0")
            state = torch.nan_to_num(state, nan=0.0)
        
        # 使用当前探索率或提供的探索率
        current_epsilon = self.eps_start if epsilon is None else epsilon

        while not done and not nan_detected:
            # 选择动作
            _, weights = self.select_action(state, current_epsilon)

            # -------- 新增: 强制权重合法化 --------
            # 为安全起见，防止后续环境因权重非法导致爆仓/异常，这里先做一次归一化与下限裁剪
            weights = torch.clamp(weights, min=1e-8)  # 保证非负且不为 0
            weights = weights / weights.sum()  # 再次归一化，确保权重之和为 1
            # ------------------------------------

            # 检查权重是否包含NaN
            if torch.isnan(weights).any():
                self.logger.warning("检测到NaN权重，使用均匀分布")
                weights = torch.ones_like(weights) / weights.size(0)
                weights = torch.clamp(weights, min=1e-8)  # 确保权重是正的，避免log(0)错误
                nan_detected = True

            # 执行动作并获取下一个状态
            try:
                next_state, reward, done, info = self.env.step(weights.cpu().numpy())

                # 检查奖励是否为NaN
                if np.isnan(reward):
                    self.logger.warning("环境返回了NaN奖励，已替换为0")
                    reward = 0.0
                    nan_detected = True
            except Exception as e:
                self.logger.error(f"环境step方法出错: {str(e)}")
                # 如果环境出错，结束此回合
                done = True
                reward = 0.0
                # 创建一个与state形状相同的零张量作为next_state
                next_state = torch.zeros_like(state)
                nan_detected = True

            # 将下一状态转为张量并移至设备
            if not isinstance(next_state, torch.Tensor):
                next_state = torch.FloatTensor(next_state).to(self.device)

            # 检查next_state是否包含NaN
            if torch.isnan(next_state).any():
                self.logger.warning("下一状态包含NaN值，已替换为0")
                next_state = torch.nan_to_num(next_state, nan=0.0)

            # 存储经验
            if not nan_detected:
                stored = self.store_transition(state, weights, reward, next_state, done)
                # 如果存储失败，可能是因为数据包含NaN
                if not stored:
                    nan_detected = True

            # 当缓冲区中有足够的样本时进行更新
            if len(self.buffer) > self.batch_size and not nan_detected:
                # 使用try-except块处理更新过程中可能出现的错误
                try:
                    # 保存当前模型参数，以便在出现问题时恢复
                    current_state_dict = {k: v.clone() for k, v in self.model.state_dict().items()}

                    # 执行更新
                    loss = self.update()

                    # 检查损失是否为NaN
                    if loss is not None and (np.isnan(loss) or np.isinf(loss)):
                        self.logger.warning(f"检测到无效损失值: {loss}，恢复模型参数")
                        # 恢复模型参数
                        self.model.load_state_dict(current_state_dict)
                        nan_detected = True
                    else:
                        if loss is not None:
                            losses.append(loss)

                        # 梯度裁剪 - 即使update方法已经包含梯度裁剪，这里也再次确认
                        for param in self.model.parameters():
                            if param.grad is not None:
                                if torch.isnan(param.grad).any() or torch.isinf(param.grad).any():
                                    self.logger.warning(f"检测到NaN或Inf梯度，已替换为0")
                                    param.grad = torch.nan_to_num(param.grad, nan=0.0, posinf=0.0, neginf=0.0)

                                # 应用梯度裁剪
                                torch.nn.utils.clip_grad_norm_(param, self.clip_grad_norm)

                    # 更新目标网络
                    if self.target_model is not None and steps % self.target_update == 0:
                        # 检查模型参数是否包含NaN
                        has_nan_params = False
                        for name, param in self.model.named_parameters():
                            if torch.isnan(param).any() or torch.isinf(param).any():
                                self.logger.warning(f"模型参数 {name} 包含NaN或Inf值")
                                has_nan_params = True

                        if not has_nan_params:
                            self.update_target_model()
                        else:
                            self.logger.warning("由于模型包含NaN值，跳过目标网络更新")
                            nan_detected = True

                except Exception as e:
                    self.logger.error(f"更新过程中出错: {str(e)}")
                    nan_detected = True

            # 更新当前状态和总奖励
            state = next_state
            episode_reward += reward
            steps += 1
            self.current_step += 1

            # 渲染环境
            if render:
                self.env.render()

            # 检查回合步数，防止回合过长
            if steps > 10000:  # 设置一个合理的最大步数限制
                self.logger.warning("回合步数超过10000，强制终止")
                done = True
        
        # 更新探索率
        self.eps_start = max(self.eps_end, self.eps_start * self.eps_decay)
        
        # 计算平均损失
        avg_loss = np.mean(losses) if losses else 0.0

        # 本回合奖励改为 "平均每步奖励"，避免随步数放大/缩小
        avg_reward = episode_reward / max(steps, 1)

        # -------- 更新回合级日志 --------
        self.logger.info(
            f"Episode {getattr(self, 'current_episode', 'N/A')} | "
            f"avg_reward={avg_reward:.4f} | loss={avg_loss:.6f} | steps={steps} | "
            f"epsilon={self.eps_start:.4f} | nan={nan_detected}")
        # --------------------------------
        
        # 返回平均奖励而非累计奖励
        return avg_reward, avg_loss, steps, nan_detected

    def update(self):
        """
        根据经验回放缓冲区中的样本更新模型参数

        Returns:
            loss: 当前批次的损失值
        """
        # 如果缓冲区中的样本不足，则返回None
        if len(self.buffer) < self.batch_size:
            return None

        # 从缓冲区中采样
        states, actions, rewards, next_states, dones = self.buffer.sample(self.batch_size)

        try:
            # 转换为张量
            states = torch.FloatTensor(states).to(self.device)
            rewards = torch.FloatTensor(rewards).to(self.device)
            next_states = torch.FloatTensor(next_states).to(self.device)
            dones = torch.FloatTensor(dones).to(self.device)

            # 处理actions，确保它是张量
            if isinstance(actions[0], np.ndarray):
                actions = torch.FloatTensor(np.vstack(actions)).to(self.device)
            else:
                actions = torch.FloatTensor(actions).to(self.device)  # 使用FloatTensor而不是LongTensor

            # 确保模型处于训练模式
            self.model.train()

            # 明确告诉优化器进入新的迭代
            self.optimizer.zero_grad()

            # 获取当前模型输出
            model_output = self.model(states)

            # 处理模型输出，可能是元组 (weights, attention_weights) 或 (weights, returns, attention_weights)
            if isinstance(model_output, tuple):
                if len(model_output) == 3:  # SupervisedAlphaPortfolio 模型
                    current_weights, predicted_returns, attention_weights = model_output
                else:  # 其他模型
                    current_weights = model_output[0]  # 只使用权重部分
            else:
                current_weights = model_output

            # 处理当前权重的维度
            if len(current_weights.shape) == 3:  # [batch_size, window_size, num_stocks]
                # 取最后一个时间步的权重
                current_weights = current_weights[:, -1, :]  # [batch_size, num_stocks]

            # 确保当前权重的维度正确
            if current_weights.size(0) != self.batch_size:
                # self.logger.warning(f"调整 current_weights 维度从 {current_weights.size()} 到 [{self.batch_size}, {current_weights.size(-1)}]")
                if current_weights.size(0) > self.batch_size:
                    current_weights = current_weights[:self.batch_size]
                else:
                    # 如果批次大小小于预期，进行扩展（重复最后一个样本）
                    padding = torch.zeros(self.batch_size - current_weights.size(0), current_weights.size(-1)).to(
                        self.device)
                    current_weights = torch.cat([current_weights, padding], dim=0)

            # 检查当前权重是否有梯度附加
            if not current_weights.requires_grad:
                self.logger.warning("当前权重没有requires_grad标志，这会导致梯度计算失败")
                # 尝试通过复制一份来重新附加梯度
                current_weights = current_weights.clone().detach().requires_grad_(True)

            # -------- 新增: 使用 reward 向量化并与动作匹配 --------
            # 奖励是标量，但目标是权重向量，因此让奖励按实际动作方向分配
            reward_vector = rewards.unsqueeze(1) * actions  # [batch, num_stocks]
            # ----------------------------------------------

            # 计算目标值 - 使用no_grad()确保目标不需要梯度
            with torch.no_grad():
                if self.target_model is not None:
                    self.target_model.eval()

                    # 获取目标模型输出
                    target_output = self.target_model(next_states)

                    # 处理目标模型输出，可能是元组
                    if isinstance(target_output, tuple):
                        if len(target_output) == 3:  # SupervisedAlphaPortfolio 模型在评估模式下返回2个值
                            next_weights, _, _ = target_output
                        else:
                            next_weights = target_output[0]  # 只使用权重部分
                    else:
                        next_weights = target_output

                    # 处理监督学习模型的输出维度
                    if len(next_weights.shape) == 3:  # [batch_size, window_size, num_stocks]
                        # 取最后一个时间步的权重
                        next_weights = next_weights[:, -1, :]  # [batch_size, num_stocks]

                    # 确保维度匹配
                    if next_weights.size(0) != self.batch_size:
                        # self.logger.warning(f"调整 next_weights 维度从 {next_weights.size()} 到 [{self.batch_size}, {next_weights.size(-1)}]")
                        if next_weights.size(0) > self.batch_size:
                            next_weights = next_weights[:self.batch_size]
                        else:
                            # 如果批次大小小于预期，进行扩展（重复最后一个样本）
                            padding = torch.zeros(self.batch_size - next_weights.size(0), next_weights.size(-1)).to(
                                self.device)
                            next_weights = torch.cat([next_weights, padding], dim=0)

                    # 计算目标值
                    target_weights = reward_vector + (1 - dones.unsqueeze(1)) * self.gamma * next_weights
                else:
                    # 如果没有目标网络，使用当前网络
                    self.model.eval()

                    # 获取当前模型输出用于目标计算
                    current_output = self.model(next_states)

                    # 处理模型输出，可能是元组
                    if isinstance(current_output, tuple):
                        if len(current_output) == 3:  # SupervisedAlphaPortfolio 模型在训练模式下返回3个值
                            next_weights, _, _ = current_output
                        else:
                            next_weights = current_output[0]  # 只使用权重部分
                    else:
                        next_weights = current_output

                    # 处理监督学习模型的输出维度
                    if len(next_weights.shape) == 3:  # [batch_size, window_size, num_stocks]
                        # 取最后一个时间步的权重
                        next_weights = next_weights[:, -1, :]  # [batch_size, num_stocks]

                    # 确保维度匹配
                    if next_weights.size(0) != self.batch_size:
                        # self.logger.warning(f"调整 next_weights 维度从 {next_weights.size()} 到 [{self.batch_size}, {next_weights.size(-1)}]")
                        if next_weights.size(0) > self.batch_size:
                            next_weights = next_weights[:self.batch_size]
                        else:
                            # 如果批次大小小于预期，进行扩展（重复最后一个样本）
                            padding = torch.zeros(self.batch_size - next_weights.size(0), next_weights.size(-1)).to(
                                self.device)
                            next_weights = torch.cat([next_weights, padding], dim=0)

                    # 计算目标值
                    target_weights = reward_vector + (1 - dones.unsqueeze(1)) * self.gamma * next_weights

            # 确保目标值分离并转换为浮点类型
            target_weights = target_weights.detach().float()

            # 确保模型重新处于训练模式
            self.model.train()

            # 检查模型参数是否需要梯度
            requires_grad = False
            for param in self.model.parameters():
                if param.requires_grad:
                    requires_grad = True
                    break

            if not requires_grad:
                self.logger.error("模型参数都不需要梯度计算，无法进行反向传播")
                return float('nan')

            # 计算损失
            loss = self.criterion(current_weights, target_weights)

            # 如果是SupervisedAlphaPortfolio模型并且有predicted_returns
            if isinstance(model_output, tuple) and len(model_output) == 3:
                # 使用一个更简单的损失函数，只是为了确保梯度能传播到return_prediction层
                dummy_loss = torch.mean(model_output[1]) * 0.0
                loss = loss + dummy_loss

            # 检查损失是否有梯度
            if not hasattr(loss, 'grad_fn') or loss.grad_fn is None:
                self.logger.error("损失函数没有梯度函数，可能是计算图断开")
                # 尝试手动添加一个需要梯度的因子
                loss = loss * 1.0  # 创建一个新的操作以附加梯度函数

            # 检查损失是否为NaN
            if torch.isnan(loss).any():
                self.logger.warning("警告: 损失值为NaN")
                return float('nan')

            # 反向传播
            loss.backward()

            # 应用梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.clip_grad_norm)

            # 检查梯度
            nan_grad = False
            for name, param in self.model.named_parameters():
                if param.grad is None:
                    self.logger.warning(f"参数 {name} 的梯度为None")
                elif torch.isnan(param.grad).any():
                    self.logger.warning(f"参数 {name} 的梯度包含NaN值")
                    param.grad[torch.isnan(param.grad)] = 0.0
                    nan_grad = True

            # 如果有NaN梯度，就不更新
            if not nan_grad:
                self.optimizer.step()

            self.train_count += 1

            # 学习率预热
            if self.current_step < self.warm_up_steps:
                lr_scale = min(1.0, float(self.current_step) / float(self.warm_up_steps))
                for pg in self.optimizer.param_groups:
                    pg['lr'] = lr_scale * self.learning_rate

            # -------- 新增: 单批次损失日志 --------
            return_loss = loss.item()
            self.logger.debug(f"batch_loss={return_loss:.6f}")
            # -------------------------------------

            return return_loss

        except Exception as e:
            self.logger.error(f"更新过程中出错: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return float('nan')
    
    def train(self, env=None, num_episodes=1000, eval_frequency=20, early_stopping_patience=50, min_delta: float =0.0):
        """
        训练模型
        
        Args:
            env: 训练环境，如果为None则使用self.env
            num_episodes: 训练回合数
            eval_frequency: 评估频率
            early_stopping_patience: 早停耐心值
            
        Returns:
            pd.DataFrame: 包含训练数据的DataFrame
        """
        if env is not None:
            self.env = env
            
        start_time = time.time()
        best_reward = -float('inf')
        no_improve_count = 0
        
        # 创建进度条
        pbar = tqdm(range(num_episodes), desc="Training")
        
        # 初始化训练记录列表
        training_records = []
        
        for episode in pbar:
            # 训练一个回合
            episode_reward, episode_loss, steps, nan_detected = self.train_episode()
            
            # 检查是否需要提前停止
            if self.should_early_stop(nan_detected):
                self.logger.warning("检测到NaN值，提前停止训练")
                break
                
            # 更新记录
            elapsed_time = time.time() - start_time
            eps = self.eps_start
            record = {
                'episode': episode + 1,
                'reward': episode_reward,
                'loss': episode_loss,
                'epsilon': eps,
                'elapsed_time': elapsed_time
            }
            
            # 定期评估
            if (episode + 1) % eval_frequency == 0:
                eval_metrics = self.evaluate_new(num_episodes=1)
                record.update({
                    'eval_episode': eval_metrics.get('episode', episode + 1),
                    'eval_reward': eval_metrics.get('reward', 0.0),
                    'eval_final_portfolio_value': eval_metrics.get('final_portfolio_value', 0.0),
                    'eval_ARR': eval_metrics.get('ARR', 0.0),
                    'eval_AVol': eval_metrics.get('AVol', 0.0),
                    'eval_MDD': eval_metrics.get('MDD', 0.0),
                    'eval_ASR': eval_metrics.get('ASR', 0.0),
                    'eval_CR': eval_metrics.get('CR', 0.0),
                    'eval_IR': eval_metrics.get('IR', 0.0),
                    'eval_win_rate': eval_metrics.get('win_rate', 0.0)
                })

                # 更新最佳奖励
                if eval_metrics['reward'] > best_reward + min_delta:
                    best_reward = eval_metrics['reward']
                    no_improve_count = 0
                else:
                    no_improve_count += 1

                #检查早停条件
                if no_improve_count >= early_stopping_patience:
                    self.logger.info(f"连续{early_stopping_patience}次评估没有改善，提前停止训练")
                    break
                    
            #更新进度条
            pbar.set_postfix({
                'reward': f"{episode_reward:.2f}",
                'loss': f"{episode_loss:.4f}",
                'epsilon': f"{eps:.3f}"
            })
            
            # 添加记录到列表
            training_records.append(record)

            # 同步当前回合号与最佳收益，以便 save_model 等接口能够正确记录
            self.current_episode = episode + 1
            if best_reward > self.best_reward:
                self.best_reward = best_reward

            model_dir = os.path.join("/root/autodl-tmp/models", self.timestamp)
            #model_dir = os.path.join("models", self.timestamp)
            os.makedirs(model_dir, exist_ok=True)
            model_path = os.path.join(model_dir, f"episode_{episode + 1}.pth")
            self.save_model(model_path)
            self.logger.info(f"[模型已保存] Episode {episode + 1} -> {model_path}")


        # 转换为DataFrame并返回
        return pd.DataFrame(training_records) 