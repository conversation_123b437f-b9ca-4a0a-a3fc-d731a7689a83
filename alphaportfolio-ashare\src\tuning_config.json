{"data": {"index_type": "hs300", "start_year": 2019, "end_year": 2023, "test_year": 2024, "top_stocks": 50, "use_core_features": true}, "tuning": {"n_trials": 50, "timeout": 3600, "n_jobs": 1, "cv_splits": 3, "seeds": [42, 123, 456, 789, 999]}, "algorithms": {"ppo": {"enabled": true, "clip_epsilon": [0.1, 0.2, 0.3], "entropy_coef": [0.01, 0.02, 0.05], "gae_lambda": [0.9, 0.95, 0.99]}, "a2c": {"enabled": true, "entropy_coef": [0.01, 0.02, 0.05], "value_coef": [0.25, 0.5, 1.0]}, "ddpg": {"enabled": false, "noise_std": [0.1, 0.2, 0.3], "tau": [0.001, 0.005, 0.01]}}, "model": {"d_model_range": [128, 256, 512], "num_heads_range": [2, 4, 8], "num_layers_range": [1, 2, 3], "dropout_range": [0.0, 0.1, 0.2]}, "training": {"max_episodes": 300, "eval_frequency": 20, "early_stopping_patience": 30, "batch_size_range": [32, 64, 128], "learning_rate_range": [1e-05, 0.001]}, "evaluation": {"metrics": ["sharpe_ratio", "total_return", "max_drawdown", "win_rate", "volatility"], "primary_metric": "sharpe_ratio", "overfitting_threshold": 0.1, "generalization_threshold": 0.8}}