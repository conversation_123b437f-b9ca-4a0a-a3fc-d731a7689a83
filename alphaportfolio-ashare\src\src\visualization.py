import matplotlib.pyplot as plt
import numpy as np
import os
from datetime import datetime
import logging

class TrainingVisualizer:
    """训练过程可视化工具"""
    
    def __init__(self, save_dir='results', timestamp=None):
        """
        初始化可视化器
        
        Args:
            save_dir: 图表保存目录
            timestamp: 统一的时间戳
        """
        self.save_dir = save_dir
        self.timestamp = timestamp or datetime.now().strftime('%Y%m%d_%H%M%S')
        self._ensure_save_dir()
        self.logger = logging.getLogger(__name__)
        
        # 存储训练数据
        self.episode_rewards = []
        self.episode_losses = []
        self.eval_metrics = {
            'avg_reward': [],
            'avg_sharpe': [],
            'avg_max_drawdown': [],
            'avg_win_rate': []
        }
        self.eval_episodes = []
        self.epsilon_values = []  # 添加存储探索率的属性
        
    def _ensure_save_dir(self):
        """确保保存目录存在"""
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)
            
    def add_episode_data(self, episode, reward, loss):
        """
        添加回合训练数据
        
        Args:
            episode: 回合数
            reward: 回合奖励
            loss: 回合损失
        """
        self.episode_rewards.append(reward)
        self.episode_losses.append(loss)
        
    def add_eval_data(self, episode, metrics):
        """
        添加评估数据
        
        Args:
            episode: 评估回合数
            metrics: 评估指标
        """
        self.eval_episodes.append(episode)
        for key in self.eval_metrics:
            self.eval_metrics[key].append(metrics[key])
            
    def plot_training_curves(self):
        """绘制训练曲线"""
        plt.figure(figsize=(15, 10))
        
        # 绘制奖励曲线
        plt.subplot(2, 2, 1)
        plt.plot(self.episode_rewards, label='Episode Reward')
        plt.title('Training Rewards')
        plt.xlabel('Episode')
        plt.ylabel('Reward')
        plt.legend()
        
        # 绘制损失曲线
        plt.subplot(2, 2, 2)
        plt.plot(self.episode_losses, label='Episode Loss')
        plt.title('Training Losses')
        plt.xlabel('Episode')
        plt.ylabel('Loss')
        plt.legend()
        
        # 绘制评估指标
        plt.subplot(2, 2, 3)
        for metric, values in self.eval_metrics.items():
            plt.plot(self.eval_episodes, values, label=metric)
        plt.title('Evaluation Metrics')
        plt.xlabel('Episode')
        plt.ylabel('Value')
        plt.legend()
        
        # 绘制移动平均奖励
        plt.subplot(2, 2, 4)
        
        # 绘制探索率和夏普比率
        if self.epsilon_values:
            # 在左Y轴绘制探索率
            plt.plot(range(1, len(self.epsilon_values) + 1), self.epsilon_values, 'b--', label='探索率 ε')
            plt.xlabel('回合')
            plt.ylabel('探索率 ε', color='b')
            plt.tick_params(axis='y', labelcolor='b')
            
            # 如果有夏普比率数据，添加到右Y轴
            if 'avg_sharpe' in self.eval_metrics and self.eval_metrics['avg_sharpe']:
                ax2 = plt.twinx()
                ax2.plot(self.eval_episodes, self.eval_metrics['avg_sharpe'], 'g-', marker='o', label='夏普比率')
                ax2.set_ylabel('夏普比率', color='g')
                ax2.tick_params(axis='y', labelcolor='g')
                
                # 合并图例
                lines, labels = plt.gca().get_legend_handles_labels()
                lines2, labels2 = ax2.get_legend_handles_labels()
                ax2.legend(lines + lines2, labels + labels2, loc='best')
            else:
                plt.legend()
            
            plt.title('探索率与夏普比率变化')
        else:
            # 如果没有探索率数据，则显示移动平均奖励
            if self.episode_rewards:
                window_size = min(50, len(self.episode_rewards))
                if window_size > 1:
                    moving_avg = np.convolve(self.episode_rewards, 
                                          np.ones(window_size)/window_size, 
                                          mode='valid')
                    plt.plot(moving_avg, label=f'{window_size}-回合移动平均')
                    plt.title('奖励移动平均')
                    plt.xlabel('回合')
                    plt.ylabel('平均奖励')
                    plt.legend()
        
        # 显示图表
        plt.tight_layout()
        
        # 保存图表
        save_path = os.path.join(self.save_dir, f'training_curves_{self.timestamp}.png')
        plt.savefig(save_path)
        plt.close()
        
        self.logger.info(f"训练曲线已保存到: {save_path}")
        
    def plot_metrics_comparison(self, metrics_history):
        """
        绘制不同参数设置的指标对比
        
        Args:
            metrics_history: 不同参数设置的评估指标历史
        """
        plt.figure(figsize=(15, 10))
        
        # 绘制不同参数设置的夏普比率对比
        plt.subplot(2, 2, 1)
        for name, metrics in metrics_history.items():
            plt.plot(metrics['avg_sharpe'], label=name)
        plt.title('Sharpe Ratio Comparison')
        plt.xlabel('Evaluation')
        plt.ylabel('Sharpe Ratio')
        plt.legend()
        
        # 绘制不同参数设置的最大回撤对比
        plt.subplot(2, 2, 2)
        for name, metrics in metrics_history.items():
            plt.plot(metrics['avg_max_drawdown'], label=name)
        plt.title('Max Drawdown Comparison')
        plt.xlabel('Evaluation')
        plt.ylabel('Max Drawdown')
        plt.legend()
        
        # 绘制不同参数设置的胜率对比
        plt.subplot(2, 2, 3)
        for name, metrics in metrics_history.items():
            plt.plot(metrics['avg_win_rate'], label=name)
        plt.title('Win Rate Comparison')
        plt.xlabel('Evaluation')
        plt.ylabel('Win Rate')
        plt.legend()
        
        # 绘制不同参数设置的平均奖励对比
        plt.subplot(2, 2, 4)
        for name, metrics in metrics_history.items():
            plt.plot(metrics['avg_reward'], label=name)
        plt.title('Average Reward Comparison')
        plt.xlabel('Evaluation')
        plt.ylabel('Average Reward')
        plt.legend()
        
        # 显示图表
        plt.tight_layout()
        
        # 保存图表
        save_path = os.path.join(self.save_dir, f'metrics_comparison_{self.timestamp}.png')
        plt.savefig(save_path)
        plt.close()
        
        self.logger.info(f"指标对比图已保存到: {save_path}")
        
    def save_metrics_to_csv(self):
        """将评估指标保存到CSV文件"""
        import pandas as pd
        
        # 创建训练数据DataFrame
        train_data = pd.DataFrame({
            'episode': range(1, len(self.episode_rewards) + 1),
            'reward': self.episode_rewards,
            'loss': self.episode_losses
        })
        
        # 创建评估数据DataFrame
        eval_data = pd.DataFrame({
            'episode': self.eval_episodes,
            **self.eval_metrics
        })
        
        # 保存到CSV
        train_path = os.path.join(self.save_dir, f'training_metrics_{self.timestamp}.csv')
        eval_path = os.path.join(self.save_dir, f'evaluation_metrics_{self.timestamp}.csv')
        
        train_data.to_csv(train_path, index=False)
        eval_data.to_csv(eval_path, index=False)
        
        self.logger.info(f"训练指标已保存到: {train_path}")
        self.logger.info(f"评估指标已保存到: {eval_path}")

    def save_metrics_to_csv_new(self, rewards_df=None):
        """将训练与评估指标保存到CSV文件"""

        # 保存训练数据
        import pandas as pd
        train_data = pd.DataFrame({
            'episode': range(1, len(self.episode_rewards) + 1),
            'reward': self.episode_rewards,
            'loss': self.episode_losses
        })

        # 保存评估数据（如果传入了rewards_df，就使用它）
        if rewards_df is not None:
            eval_data = rewards_df[[
                'episode',
                'eval_reward',
                'eval_final_portfolio_value',
                'eval_ARR',
                'eval_AVol',
                'eval_MDD',
                'eval_ASR',
                'eval_CR',
                'eval_IR',
                'eval_win_rate'
            ]].dropna(subset=['eval_reward'])  # 只保留真正评估过的行
        else:
            eval_data = pd.DataFrame({
                'episode': self.eval_episodes,
                **self.eval_metrics
            })

        # 保存为CSV
        train_path = os.path.join(self.save_dir, f'training_metrics_{self.timestamp}.csv')
        eval_path = os.path.join(self.save_dir, f'evaluation_metrics_{self.timestamp}.csv')

        train_data.to_csv(train_path, index=False)
        eval_data.to_csv(eval_path, index=False)

        self.logger.info(f"训练指标已保存到: {train_path}")
        self.logger.info(f"评估指标已保存到: {eval_path}")
