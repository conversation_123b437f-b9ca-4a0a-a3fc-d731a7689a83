"""
Financial Metrics Module

Provides essential financial performance metrics as a replacement for quantstats
when it's not available due to dependency issues.
"""

import numpy as np
import pandas as pd
from typing import Union, Optional


def calculate_returns(prices: Union[pd.Series, np.ndarray]) -> Union[pd.Series, np.ndarray]:
    """Calculate returns from price series"""
    if isinstance(prices, pd.Series):
        return prices.pct_change().dropna()
    else:
        return np.diff(prices) / prices[:-1]


def sharpe_ratio(returns: Union[pd.Series, np.ndarray], 
                risk_free_rate: float = 0.0,
                periods: int = 252) -> float:
    """
    Calculate Sharpe ratio
    
    Args:
        returns: Return series
        risk_free_rate: Risk-free rate (annualized)
        periods: Number of periods per year (252 for daily)
    """
    if isinstance(returns, pd.Series):
        returns = returns.dropna()
    
    if len(returns) == 0:
        return 0.0
    
    excess_returns = returns - risk_free_rate / periods
    
    if np.std(excess_returns) == 0:
        return 0.0
    
    return np.sqrt(periods) * np.mean(excess_returns) / np.std(excess_returns)


def max_drawdown(returns: Union[pd.Series, np.ndarray]) -> float:
    """Calculate maximum drawdown"""
    if isinstance(returns, pd.Series):
        returns = returns.dropna()
    
    if len(returns) == 0:
        return 0.0
    
    cumulative = np.cumprod(1 + returns)
    running_max = np.maximum.accumulate(cumulative)
    drawdown = (cumulative - running_max) / running_max
    
    return np.min(drawdown)


def total_return(returns: Union[pd.Series, np.ndarray]) -> float:
    """Calculate total return"""
    if isinstance(returns, pd.Series):
        returns = returns.dropna()
    
    if len(returns) == 0:
        return 0.0
    
    return np.prod(1 + returns) - 1


def volatility(returns: Union[pd.Series, np.ndarray], periods: int = 252) -> float:
    """Calculate annualized volatility"""
    if isinstance(returns, pd.Series):
        returns = returns.dropna()
    
    if len(returns) == 0:
        return 0.0
    
    return np.std(returns) * np.sqrt(periods)


def win_rate(returns: Union[pd.Series, np.ndarray]) -> float:
    """Calculate win rate (percentage of positive returns)"""
    if isinstance(returns, pd.Series):
        returns = returns.dropna()
    
    if len(returns) == 0:
        return 0.0
    
    return np.sum(returns > 0) / len(returns)


def calmar_ratio(returns: Union[pd.Series, np.ndarray], periods: int = 252) -> float:
    """Calculate Calmar ratio (annualized return / max drawdown)"""
    if isinstance(returns, pd.Series):
        returns = returns.dropna()
    
    if len(returns) == 0:
        return 0.0
    
    annual_return = (np.prod(1 + returns) ** (periods / len(returns))) - 1
    max_dd = abs(max_drawdown(returns))
    
    if max_dd == 0:
        return np.inf if annual_return > 0 else 0.0
    
    return annual_return / max_dd


def sortino_ratio(returns: Union[pd.Series, np.ndarray], 
                 risk_free_rate: float = 0.0,
                 periods: int = 252) -> float:
    """Calculate Sortino ratio"""
    if isinstance(returns, pd.Series):
        returns = returns.dropna()
    
    if len(returns) == 0:
        return 0.0
    
    excess_returns = returns - risk_free_rate / periods
    downside_returns = excess_returns[excess_returns < 0]
    
    if len(downside_returns) == 0:
        return np.inf if np.mean(excess_returns) > 0 else 0.0
    
    downside_deviation = np.sqrt(np.mean(downside_returns ** 2))
    
    if downside_deviation == 0:
        return 0.0
    
    return np.sqrt(periods) * np.mean(excess_returns) / downside_deviation


def calculate_performance_metrics(returns: Union[pd.Series, np.ndarray], 
                                benchmark_returns: Optional[Union[pd.Series, np.ndarray]] = None) -> dict:
    """
    Calculate comprehensive performance metrics
    
    Args:
        returns: Strategy returns
        benchmark_returns: Benchmark returns (optional)
    
    Returns:
        Dictionary of performance metrics
    """
    if isinstance(returns, pd.Series):
        returns = returns.dropna()
    
    if len(returns) == 0:
        return {
            'total_return': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'volatility': 0.0,
            'win_rate': 0.0,
            'calmar_ratio': 0.0,
            'sortino_ratio': 0.0
        }
    
    metrics = {
        'total_return': total_return(returns),
        'sharpe_ratio': sharpe_ratio(returns),
        'max_drawdown': max_drawdown(returns),
        'volatility': volatility(returns),
        'win_rate': win_rate(returns),
        'calmar_ratio': calmar_ratio(returns),
        'sortino_ratio': sortino_ratio(returns)
    }
    
    # Add benchmark comparison if provided
    if benchmark_returns is not None:
        if isinstance(benchmark_returns, pd.Series):
            benchmark_returns = benchmark_returns.dropna()
        
        if len(benchmark_returns) > 0:
            # Align returns if they're pandas series
            if isinstance(returns, pd.Series) and isinstance(benchmark_returns, pd.Series):
                aligned_returns, aligned_benchmark = returns.align(benchmark_returns, join='inner')
                excess_returns = aligned_returns - aligned_benchmark
            else:
                min_len = min(len(returns), len(benchmark_returns))
                excess_returns = returns[:min_len] - benchmark_returns[:min_len]
            
            metrics.update({
                'benchmark_total_return': total_return(benchmark_returns),
                'benchmark_sharpe_ratio': sharpe_ratio(benchmark_returns),
                'benchmark_volatility': volatility(benchmark_returns),
                'excess_return': total_return(excess_returns),
                'information_ratio': sharpe_ratio(excess_returns),
                'beta': calculate_beta(returns, benchmark_returns),
                'alpha': calculate_alpha(returns, benchmark_returns)
            })
    
    return metrics


def calculate_beta(returns: Union[pd.Series, np.ndarray], 
                  benchmark_returns: Union[pd.Series, np.ndarray]) -> float:
    """Calculate beta relative to benchmark"""
    if isinstance(returns, pd.Series) and isinstance(benchmark_returns, pd.Series):
        aligned_returns, aligned_benchmark = returns.align(benchmark_returns, join='inner')
        returns = aligned_returns.values
        benchmark_returns = aligned_benchmark.values
    else:
        min_len = min(len(returns), len(benchmark_returns))
        returns = returns[:min_len]
        benchmark_returns = benchmark_returns[:min_len]
    
    if len(returns) == 0 or np.var(benchmark_returns) == 0:
        return 1.0
    
    return np.cov(returns, benchmark_returns)[0, 1] / np.var(benchmark_returns)


def calculate_alpha(returns: Union[pd.Series, np.ndarray], 
                   benchmark_returns: Union[pd.Series, np.ndarray],
                   risk_free_rate: float = 0.0,
                   periods: int = 252) -> float:
    """Calculate alpha (Jensen's alpha)"""
    if isinstance(returns, pd.Series) and isinstance(benchmark_returns, pd.Series):
        aligned_returns, aligned_benchmark = returns.align(benchmark_returns, join='inner')
        returns = aligned_returns.values
        benchmark_returns = aligned_benchmark.values
    else:
        min_len = min(len(returns), len(benchmark_returns))
        returns = returns[:min_len]
        benchmark_returns = benchmark_returns[:min_len]
    
    if len(returns) == 0:
        return 0.0
    
    beta = calculate_beta(returns, benchmark_returns)
    
    portfolio_return = np.mean(returns) * periods
    benchmark_return = np.mean(benchmark_returns) * periods
    
    return portfolio_return - (risk_free_rate + beta * (benchmark_return - risk_free_rate))


# Compatibility functions to match quantstats interface
class QuantStatsCompat:
    """Compatibility class to mimic quantstats interface"""
    
    @staticmethod
    def stats(returns, benchmark=None, rf=0.0):
        """Generate performance statistics"""
        return calculate_performance_metrics(returns, benchmark)
    
    @staticmethod
    def sharpe(returns, rf=0.0, periods=252):
        """Calculate Sharpe ratio"""
        return sharpe_ratio(returns, rf, periods)
    
    @staticmethod
    def max_drawdown(returns):
        """Calculate maximum drawdown"""
        return max_drawdown(returns)
    
    @staticmethod
    def cagr(returns):
        """Calculate Compound Annual Growth Rate"""
        if isinstance(returns, pd.Series):
            returns = returns.dropna()
        
        if len(returns) == 0:
            return 0.0
        
        total_ret = total_return(returns)
        years = len(returns) / 252  # Assuming daily returns
        
        if years <= 0:
            return 0.0
        
        return (1 + total_ret) ** (1 / years) - 1


# Create a quantstats-like module
quantstats = QuantStatsCompat()
