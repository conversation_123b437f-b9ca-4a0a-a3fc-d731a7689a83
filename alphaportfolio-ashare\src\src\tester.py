import logging
import argparse
import torch
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import os
import quantstats as qs
import pandas as pd
from data_processor import AShareDataProcessor
from models import AlphaPortfolio
from environment import PortfolioEnv

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='AlphaPortfolio Testing')
    
    parser.add_argument('--index_type', type=str, default='hs300',
                      choices=['hs300', 'zz500', 'zz1000'],
                      help='指数类型')
    parser.add_argument('--test_year', type=int, required=True,
                      help='测试年份')
    parser.add_argument('--model_path', type=str, required=True,
                      help='模型路径')
    parser.add_argument('--window_size', type=int, default=50,
                      help='观察窗口大小')
    parser.add_argument('--initial_balance', type=float, default=1000000.0,
                      help='初始资金')
    parser.add_argument('--transaction_cost_pct', type=float, default=0.001,
                      help='交易成本百分比')
    parser.add_argument('--output_dir', type=str, default='results',
                      help='结果输出目录')
    parser.add_argument('--top_stocks', type=int, default=None,
                      help='使用交易量最大的前N支股票，默认使用全部')
    parser.add_argument('--use_core_features', action='store_true',
                      help='仅使用核心特征子集，可显著加快训练')
    parser.add_argument('--feature_list', type=str, default=None,
                      help='要使用的特征列表，以逗号分隔，例如: "close,open,volume"')
    parser.add_argument('--G', type=int, default=10, help='多空组合的G参数，决定多头和空头各自的资产数量')
    
    return parser.parse_args()


def evaluate_model(model, env, device):
    """
    评估模型性能
    
    Args:
        model: 要评估的模型
        env: 评估环境
        device: 计算设备
        
    Returns:
        dict: 评估指标
    """
    model.eval()
    state = env.reset()
    done = False
    total_reward = 0
    portfolio_values = []
    actions_history = []
    
    while not done:
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(device)
        with torch.no_grad():
            weights, _ = model(state_tensor)  # 解包返回的元组
            action = weights.cpu().numpy()[0]
        
        next_state, reward, done, _ = env.step(action)
        state = next_state
        total_reward += reward
        portfolio_values.append(env.portfolio_value)
        actions_history.append(action)
    
    # 计算评估指标
    portfolio_values = np.array(portfolio_values)
    returns = np.diff(portfolio_values) / portfolio_values[:-1]
    
    # 计算夏普比率
    sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)
    
    # 计算最大回撤
    max_drawdown = np.min(portfolio_values) / np.max(portfolio_values) - 1

    # 计算胜率
    win_rate = np.mean(returns > 0)
    
    return {
        'total_reward': total_reward,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'win_rate': win_rate,
        'portfolio_values': portfolio_values,
        'actions_history': actions_history
    }


def evaluate_model_new(model, env, device, benchmark_path=None, risk_free_rate=0.02):
    """
    评估模型性能，使用 quantstats 统一计算指标

    Args:
        model: 要评估的模型
        env: 评估环境
        device: 计算设备
        benchmark_path: 可选，benchmark CSV 路径（包含 trade_date 和 returns）
        risk_free_rate: 年无风险利率

    Returns:
        dict: 包含评估指标的字典
    """
    model.eval()
    state = env.reset()
    done = False
    total_reward = 0
    portfolio_values = []
    actions_history = []

    while not done:
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(device)
        with torch.no_grad():
            weights, _ = model(state_tensor)
            action = weights.cpu().numpy()[0]

        next_state, reward, done, _ = env.step(action)
        state = next_state
        total_reward += reward
        portfolio_values.append(env.portfolio_value)
        actions_history.append(action)

    if len(portfolio_values) >= 2:
        portfolio_values = np.array(portfolio_values)
        daily_ret = (portfolio_values[1:] - portfolio_values[:-1]) / (portfolio_values[:-1] + 1e-8)
        date_index = pd.date_range(start="2000-01-01", periods=len(daily_ret), freq='D')
        returns_series = pd.Series(daily_ret, index=date_index)

        ARR = qs.stats.cagr(returns_series, periods=365)
        AVol = qs.stats.volatility(returns_series, annualize=True)
        MDD = qs.stats.max_drawdown(returns_series)
        ASR = qs.stats.sharpe(returns_series, rf=risk_free_rate)
        CR = qs.stats.calmar(returns_series)

        # IR 计算（可选）
        IR = 0.0
        if benchmark_path is not None:
            try:
                benchmark_df = pd.read_csv(benchmark_path, parse_dates=["trade_date"])
                benchmark_returns = pd.Series(data=benchmark_df["returns"].values, index=benchmark_df["trade_date"])
                benchmark_returns = benchmark_returns[:len(returns_series)]
                IR = qs.stats.information_ratio(returns_series, benchmark_returns)
            except Exception as e:
                print(f"[Warning] benchmark 加载失败：{e}")
                IR = 0.0

        # 计算胜率
        win_rate = np.mean(daily_ret > 0)
    else:
        ARR = AVol = MDD = ASR = CR = IR = win_rate = 0.0
        portfolio_values = np.array(portfolio_values)

    return {
        'total_reward': total_reward,
        'ARR': ARR,
        'AVol': AVol,
        'MDD': MDD,
        'ASR': ASR,
        'CR': CR,
        'IR': IR,
        'win_rate': win_rate,
        'portfolio_values': portfolio_values,
        'actions_history': actions_history
    }

def test_save(model, env, device, top_k=15, save_path="results/test_output"):
    """
    在测试环境中评估模型，并保存每一天的组合收益、权重和资产值
    """
    model.eval()
    state = env.reset()
    done = False

    portfolio_values = []
    actions_history = []
    date_list = []

    while not done:
        if isinstance(state, np.ndarray):
            state_tensor = torch.FloatTensor(state).to(device)
        else:
            state_tensor = state.to(device)

        with torch.no_grad():
            weights, _ = model(state_tensor)
            action = weights.cpu().numpy()[0]

        actions_history.append(action)

        if hasattr(env, "current_date"):
            date_list.append(env.current_date)
        elif hasattr(env, "dates") and hasattr(env, "current_step"):
            date_list.append(env.dates[env.current_step])
        else:
            date_list.append(f"Day{len(portfolio_values) + 1}")

        next_state, reward, done, info = env.step(action)
        portfolio_values.append(info["portfolio_value"])
        state = next_state

    os.makedirs(save_path, exist_ok=True)

    # --- 组合收益率计算（含第一天）
    portfolio_values = np.array(portfolio_values)
    portfolio_values_shifted = np.insert(portfolio_values[:-1], 0, env.initial_balance)
    capital_returns = (portfolio_values - portfolio_values_shifted) / (portfolio_values_shifted + 1e-8)

    returns_df = pd.DataFrame({
        "date": date_list,
        "daily_return": capital_returns
    })
    returns_df.to_csv(os.path.join(save_path, "daily_returns.csv"), index=False)

    # --- 保存每日资产（capital）
    capital_df = pd.DataFrame({
        "date": date_list,
        "capital": portfolio_values
    })
    capital_df.to_csv(os.path.join(save_path, "daily_capital.csv"), index=False)

    # --- 保存每日 Top-K 权重（每行一个股票）
    if hasattr(env, "stock_codes"):
        stock_names = env.stock_codes
    else:
        stock_names = [f"stock_{i + 1}" for i in range(len(actions_history[0]))]

    weights_df = pd.DataFrame(actions_history, columns=stock_names)

    topk_rows = []
    for i in range(len(date_list)):
        weights = weights_df.iloc[i].values
        codes = weights_df.columns
        topk_idx = weights.argsort()[-top_k:][::-1]
        topk_codes = [codes[j] for j in topk_idx]
        topk_weights = [weights[j] for j in topk_idx]

        for code, weight in zip(topk_codes, topk_weights):
            topk_rows.append({
                "date": date_list[i],
                "stock_name": code,
                "weight": round(weight, 6)
            })

    topk_df = pd.DataFrame(topk_rows)
    topk_df.to_csv(os.path.join(save_path, f"daily_weights.csv"), index=False)


# def plot_results(metrics, output_dir, test_year):
#     """
#     绘制评估结果
#
#     Args:
#         metrics: 评估指标
#         output_dir: 输出目录
#         test_year: 测试年份
#     """
#     # 创建输出目录
#     os.makedirs(output_dir, exist_ok=True)
#
#     # 绘制投资组合价值曲线
#     plt.figure(figsize=(12, 6))
#     plt.plot(metrics['portfolio_values'])
#     plt.title(f'Portfolio Value - {test_year}')
#     plt.xlabel('Trading Days')
#     plt.ylabel('Portfolio Value')
#     plt.grid(True)
#     plt.savefig(os.path.join(output_dir, f'portfolio_value_{test_year}.png'))
#     plt.close()
#
#     # 绘制交易行为热图
#     actions = np.array(metrics['actions_history'])
#     plt.figure(figsize=(12, 6))
#     plt.imshow(actions.T, aspect='auto', cmap='viridis')
#     plt.colorbar(label='Portfolio Weight')
#     plt.title(f'Trading Actions - {test_year}')
#     plt.xlabel('Trading Days')
#     plt.ylabel('Assets')
#     plt.savefig(os.path.join(output_dir, f'trading_actions_{test_year}.png'))
#     plt.close()

def main():
    """主函数"""
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # 解析参数
    args = parse_args()
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f'Using device: {device}')
    
    try:
        # 设置特征子集
        feature_subset = None
        if args.use_core_features:
            # 获取核心特征列表
            data_processor = AShareDataProcessor(index_type=args.index_type)
            feature_subset = data_processor.get_core_features()
            logger.info(f"使用核心特征子集: {feature_subset}")
        elif args.feature_list:
            # 使用用户指定的特征列表
            feature_subset = [f.strip() for f in args.feature_list.split(',')]
            logger.info(f"使用用户指定特征: {feature_subset}")
            
        # 准备测试数据
        logger.info('Preparing test data...')
        data_processor = AShareDataProcessor(
            index_type=args.index_type,
            top_stocks=args.top_stocks,
            feature_subset=feature_subset
        )
        test_data = data_processor.prepare_test_data(args.test_year)
        
        if test_data is None:
            logger.error('Failed to prepare test data')
            return
            
        # 创建测试环境
        logger.info('Creating test environment...')
        env = PortfolioEnv(
            data=test_data,
            initial_balance=args.initial_balance,
            transaction_cost_pct=args.transaction_cost_pct,
            window_size=20
        )
        #print(len(env.data.columns.tolist()))
        #print(env.data.columns.tolist())
        # print(env.data['trade_date'].min())
        # print(env.data['trade_date'].max())
        # print(env.data['trade_date'].nunique())
        #print(env.window_size)
        # 加载模型
        logger.info('Loading model...')
        
        # 先加载检查点以获取原始模型配置
        checkpoint = torch.load(args.model_path)
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            model_state_dict = checkpoint['model_state_dict']
            # 从状态字典中获取原始输入维度
            original_input_dim = model_state_dict['feature_processor.0.weight'].shape[1]
            logger.info(f"原始模型输入维度: {original_input_dim}")
        else:
            model_state_dict = checkpoint
            original_input_dim = model_state_dict['feature_processor.0.weight'].shape[1]
            logger.info(f"原始模型输入维度: {original_input_dim}")
        
        # 创建模型时使用原始输入维度
        model = AlphaPortfolio(
            input_dim=original_input_dim,
            d_model=256,
            num_heads=4,
            d_ff=1024,
            num_layers=2,
            dropout=0.1,
            eps=1e-8,
            weight_clipvalue=5.0,
            G=args.G
        ).to(device)
        
        # 加载模型状态
        model.load_state_dict(model_state_dict)
        model.eval()
        
        # 检查当前环境的输入维度是否与模型匹配
        current_input_dim = env.observation_space.shape[-1]
        if current_input_dim != original_input_dim:
            logger.warning(
                f"警告: 环境输入维度({current_input_dim})与模型输入维度({original_input_dim})不匹配\n"
                f"请确保测试时使用的特征集与训练时相同"
            )
            return
        
        # 评估模型
        logger.info('Evaluating model...')
        #metrics = evaluate_model(model, env, device)
        metrics = evaluate_model_new(model, env, device, benchmark_path="dataset/benchmark_returns.csv")

        # 保存每一天的收益和权重
        logger.info('Saving per-day returns and weights...')
        test_save(model, env, device, top_k=args.top_stocks,
                  save_path=os.path.join(args.output_dir, f"test_{args.test_year}"))

        # 输出评估结果
        logger.info(
            f"\n评估结果:\n"
            f"总收益: {metrics['total_reward']:.2f}\n"
            f"ARR: {metrics['ARR']:.2f}\n"
            f"AVol: {metrics['AVol']:.2f}\n"
            f"ASR: {metrics['ASR']:.2f}\n"
            f"MDD: {metrics['MDD']:.2f}\n"
            f"CR: {metrics['CR']:.2f}\n"
            f"IR: {metrics['IR']:.2f}\n"
            f"胜率: {metrics['win_rate']:.2f}"
        )
        
        # 绘制结果
        #logger.info('Plotting results...')
        #plot_results(metrics, args.output_dir, args.test_year)

        logger.info('Testing completed successfully!')
        
    except Exception as e:
        logger.error(f'An error occurred: {str(e)}')
        raise

if __name__ == '__main__':
    main() 