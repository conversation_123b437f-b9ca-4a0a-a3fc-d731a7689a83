import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
import logging
from typing import Tuple, Optional, Dict, Any
from tqdm import tqdm
import gc

class MultiHeadAttention(nn.Module):
    """多头注意力机制"""
    
    def __init__(self, d_model, num_heads):
        super(MultiHeadAttention, self).__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        self.W_q = nn.Linear(d_model, d_model)
        self.W_k = nn.Linear(d_model, d_model)
        self.W_v = nn.Linear(d_model, d_model)
        self.W_o = nn.Linear(d_model, d_model)
        
    def forward(self, Q, K, V, mask=None):
        batch_size = Q.size(0)
        
        # 线性变换
        Q = self.W_q(Q).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        K = self.W_k(K).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        V = self.W_v(V).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        
        # 计算注意力
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
            
        attention = F.softmax(scores, dim=-1)
        
        # 计算输出
        output = torch.matmul(attention, V)
        output = output.transpose(1, 2).contiguous().view(batch_size, -1, self.d_model)
        output = self.W_o(output)
        
        return output, attention

class PositionalEncoding(nn.Module):
    """位置编码
    
    为输入序列添加位置信息，使模型能够利用序列的顺序信息。
    使用正弦和余弦函数生成位置编码。
    """
    
    def __init__(self, d_model: int, dropout: float = 0.1, max_seq_length: int = 5000):
        """
        初始化位置编码
        
        Args:
            d_model: 模型维度
            dropout: Dropout比率
            max_seq_length: 最大序列长度
        """
        super(PositionalEncoding, self).__init__()
        
        self.dropout = nn.Dropout(p=dropout)
        
        # 创建位置编码矩阵
        pe = torch.zeros(max_seq_length, d_model)
        position = torch.arange(0, max_seq_length, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-np.log(10000.0) / d_model))
        
        # 计算位置编码
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        
        # 注册为缓冲区
        self.register_buffer('pe', pe)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量，形状为 [batch_size, seq_len, d_model]
            
        Returns:
            torch.Tensor: 添加位置编码后的张量
        """
        # 使用加法而不是就地操作，确保梯度正确流动
        pe = self.pe[:, :x.size(1), :].expand(x.size(0), -1, -1)
        # 如果在训练中，确保输出需要梯度
        if self.training and not x.requires_grad:
            x = x.clone().detach().requires_grad_(True)
        return self.dropout(x + pe)

class TransformerEncoder(nn.Module):
    """Transformer编码器"""
    
    def __init__(self, d_model, num_heads, d_ff, dropout=0.1):
        super(TransformerEncoder, self).__init__()
        
        self.self_attn = MultiHeadAttention(d_model, num_heads)
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Linear(d_ff, d_model)
        )
        
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, mask=None):
        # 自注意力
        attn_output, _ = self.self_attn(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # 前馈网络
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x

class CAAN(nn.Module):
    """跨资产注意力网络 (Cross-Asset Attention Network)
    
    论文中的关键组件，用于捕获不同资产之间的相关性。
    通过多头注意力机制，学习资产间的复杂依赖关系。
    """
    
    def __init__(self, d_model: int, num_heads: int = 4, dropout: float = 0.1):
        """
        初始化跨资产注意力网络
        
        Args:
            d_model: 模型维度
            num_heads: 注意力头数
            dropout: Dropout比率
        """
        super(CAAN, self).__init__()
        
        self.d_model = d_model
        self.num_heads = num_heads
        
        # 确保模型维度能被头数整除
        assert d_model % num_heads == 0, "d_model必须能被num_heads整除"
        
        self.d_k = d_model // num_heads
        
        # 线性变换
        self.query = nn.Linear(d_model, d_model, bias=False)
        self.key = nn.Linear(d_model, d_model, bias=False)
        self.value = nn.Linear(d_model, d_model, bias=False)
        
        # 输出线性层
        self.output = nn.Linear(d_model, d_model)
        
        # 层标准化和Dropout
        self.norm = nn.LayerNorm(d_model, eps=1e-6)
        self.dropout = nn.Dropout(dropout)
        
        # 初始化参数
        self._reset_parameters()
        
    def _reset_parameters(self):
        """初始化参数"""
        # 使用Xavier初始化线性层权重
        nn.init.xavier_uniform_(self.query.weight)
        nn.init.xavier_uniform_(self.key.weight)
        nn.init.xavier_uniform_(self.value.weight)
        nn.init.xavier_uniform_(self.output.weight)
        
        # 如果有偏置，初始化为0
        if self.output.bias is not None:
            nn.init.zeros_(self.output.bias)
            
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入张量，形状为 [batch_size, num_stocks, d_model]
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 
                - 注意力输出，形状为 [batch_size, num_stocks, d_model]
                - 注意力权重，形状为 [batch_size, num_heads, num_stocks, num_stocks]
        """
        batch_size = x.size(0)
        num_stocks = x.size(1)
        
        # 残差连接
        residual = x
        
        # 线性变换
        q = self.query(x).view(batch_size, num_stocks, self.num_heads, self.d_k).transpose(1, 2)
        k = self.key(x).view(batch_size, num_stocks, self.num_heads, self.d_k).transpose(1, 2)
        v = self.value(x).view(batch_size, num_stocks, self.num_heads, self.d_k).transpose(1, 2)
        
        # 缩放点积注意力
        scores = torch.matmul(q, k.transpose(-2, -1)) / np.sqrt(self.d_k)
        
        # 对分数应用softmax
        attn = F.softmax(scores, dim=-1)
        
        # 应用dropout
        attn = self.dropout(attn)
        
        # 计算注意力输出
        output = torch.matmul(attn, v)
        
        # 重新排列维度
        output = output.transpose(1, 2).contiguous().view(batch_size, num_stocks, self.d_model)
        
        # 应用输出线性层
        output = self.output(output)
        
        # 应用dropout
        output = self.dropout(output)
        
        # 应用残差连接和层标准化
        output = self.norm(residual + output)
        
        return output, attn

class DoubleIdentity(nn.Module):
    """用于消融实验的CAAN替代模块，直接返回输入"""
    def __init__(self):
        super().__init__()
    
    def forward(self, x):
        # 返回输入和零张量，保持与CAAN相同的接口
        return x, torch.zeros_like(x)

class AlphaPortfolio(nn.Module):
    """AlphaPortfolio: 直接通过深度强化学习构建投资组合
    
    论文：AlphaPortfolio: Direct Construction Through Deep Reinforcement Learning and Interpretable AI
    
    架构：
    1. 特征降维层：将高维特征降维到合适维度
    2. 特征嵌入层：将降维后的特征映射到模型维度
    3. 位置编码：添加时序信息
    4. Transformer编码器：处理序列特征
    5. 跨资产注意力网络：捕获资产间关系
    6. 投资组合生成网络：输出权重
    """
    
    def __init__(self, input_dim: int, d_model: int = 256, num_heads: int = 4, 
                 d_ff: int = 1024, num_layers: int = 2, dropout: float = 0.1,
                 eps: float = 1e-8, weight_clipvalue: float = 5.0, G: int = 10):
        """
        初始化 AlphaPortfolio 模型
        
        Args:
            input_dim: 输入特征维度
            d_model: 模型隐藏层维度
            num_heads: 注意力头数
            d_ff: 前馈网络维度
            num_layers: Transformer 编码器层数
            dropout: Dropout 比率
            eps: 用于数值稳定性的小值
            weight_clipvalue: 权重裁剪值
            G: 参数G
        """
        super().__init__()
        
        # 初始化日志
        self.logger = logging.getLogger(__name__)
        
        self.input_dim = input_dim
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_ff = d_ff
        self.num_layers = num_layers
        self.dropout = dropout
        self.eps = eps
        self.weight_clipvalue = weight_clipvalue
        self.G = G
        
        # 特征处理器
        self.feature_processor = nn.Sequential(
            nn.Linear(input_dim, d_model),
            nn.LayerNorm(d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model),
            nn.LayerNorm(d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model),
            nn.LayerNorm(d_model)
        )
        
        # 初始化权重
        for module in self.feature_processor.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.LayerNorm):
                nn.init.ones_(module.weight)
                nn.init.zeros_(module.bias)
        
        # 2. 位置编码
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        
        # 3. Transformer 编码器层
        encoder_layers = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=num_heads,
            dim_feedforward=d_ff,
            dropout=dropout,
            activation='gelu',  # 使用GELU激活函数增加平滑性
            batch_first=True,
            norm_first=True  # 使用Pre-LN架构增加稳定性
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layers,
            num_layers=num_layers,
            enable_nested_tensor=False  # 禁用嵌套张量以消除警告
        )
        
        # 4. 跨资产注意力网络
        self.caan = CAAN(d_model, num_heads, dropout)
        
        # 5. 投资组合生成网络
        self.portfolio_generator = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2, eps=1e-6),
            nn.LeakyReLU(0.2),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 32),
            nn.LayerNorm(32, eps=1e-6),
            nn.LeakyReLU(0.2),
            nn.Dropout(dropout),
            nn.Linear(32, 1)  # 输出单个权重
        )
        
    def get_params(self) -> Dict[str, Any]:
        """获取模型参数，用于保存和加载模型"""
        return {
            'input_dim': self.input_dim,
            'd_model': self.d_model,
            'num_heads': self.num_heads,
            'd_ff': self.d_ff,
            'num_layers': self.num_layers,
            'dropout': self.dropout
        }
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        AlphaPortfolio模型的前向传播
        
        参数:
            x: 输入张量, 形状可能为以下几种:
               - [batch_size, seq_len, num_stocks, num_features]
               - [seq_len, num_stocks, num_features] - 单批次情况
               - [batch_size, window_size*num_stocks*num_features] - 展平的情况
        
        返回:
            weights: 投资组合权重, 形状为 [batch_size, num_stocks]
            attention_weights: 注意力权重, 用于解释模型决策
        """
        try:
            # 处理不同形状的输入
            input_dim = self.input_dim  # 模型预期的输入特征维度
            
            # 根据输入维度动态调整处理方式
            input_shape = x.shape
            
            if len(input_shape) == 3:  # [seq_len, num_stocks, num_features]
                # 单批次情况，添加批次维度
                seq_len, num_stocks, num_features = input_shape
                batch_size = 1
                x = x.unsqueeze(0)
                # 只在第一个批次打印形状信息
                if not self.training and not hasattr(self, '_shape_logged'):
                    self.logger.info(f"添加批次维度后的形状: {x.shape}")
                    self._shape_logged = True
                # 更新input_shape
                input_shape = x.shape
            
            if len(input_shape) == 4:  # [batch_size, seq_len, num_stocks, num_features]
                # 标准的4维输入
                batch_size, seq_len, num_stocks, num_features = input_shape
            else:
                raise ValueError(f"无法处理的输入维度: {len(input_shape)}, 形状: {input_shape}")
            
            # 检查输入中是否有NaN值或无穷大值
            if torch.isnan(x).any() or torch.isinf(x).any():
                # 使用torch.where替换NaN和Inf值，保留梯度流
                is_nan = torch.isnan(x)
                is_inf_pos = torch.isinf(x) & (x > 0)
                is_inf_neg = torch.isinf(x) & (x < 0)
                
                x = torch.where(is_nan, torch.zeros_like(x), x)
                x = torch.where(is_inf_pos, torch.ones_like(x) * 1e6, x)
                x = torch.where(is_inf_neg, torch.ones_like(x) * -1e6, x)
                
                if not self.training:  # 只在评估模式下打印
                    self.logger.warning(f"警告: 输入张量包含NaN或Inf值，已替换")
            
            # 1. 特征处理
            # 将张量展平为 [batch_size*seq_len*num_stocks, num_features]
            flattened_x = x.reshape(-1, num_features)
            
            # 应用数值稳定性限制，但保留梯度
            flattened_x = torch.clamp(flattened_x, min=-1e6, max=1e6)
            
            # 确保输入维度与特征处理器匹配
            if num_features != self.input_dim:
                if num_features > self.input_dim:
                    # 截取前self.input_dim个特征
                    flattened_x = flattened_x[:, :self.input_dim]
                else:
                    # 用零填充缺失的特征
                    padding = torch.zeros((flattened_x.shape[0], self.input_dim - num_features), 
                                         device=flattened_x.device, 
                                         dtype=flattened_x.dtype)
                    flattened_x = torch.cat([flattened_x, padding], dim=1)
            
            # 应用特征处理器
            features = self.feature_processor(flattened_x)
            
            # 检查特征处理后的输出是否有NaN/Inf
            if torch.isnan(features).any() or torch.isinf(features).any():
                is_nan = torch.isnan(features)
                is_inf_pos = torch.isinf(features) & (features > 0)
                is_inf_neg = torch.isinf(features) & (features < 0)
                
                features = torch.where(is_nan, torch.zeros_like(features), features)
                features = torch.where(is_inf_pos, torch.ones_like(features) * 1e6, features)
                features = torch.where(is_inf_neg, torch.ones_like(features) * -1e6, features)
                
                if not self.training:  # 只在评估模式下打印
                    self.logger.warning(f"警告: 特征处理后包含NaN或Inf值，已替换")
            
            # 重塑回 [batch_size, seq_len, num_stocks, d_model]
            features = features.reshape(batch_size, seq_len, num_stocks, self.d_model)
            
            # 2. 应用位置编码
            encoded_features = torch.zeros_like(features)
            for i in range(num_stocks):
                stock_features = features[:, :, i, :]  # [batch_size, seq_len, d_model]
                encoded_stock = self.pos_encoder(stock_features)
                
                # 检查位置编码后是否有NaN/Inf
                if torch.isnan(encoded_stock).any() or torch.isinf(encoded_stock).any():
                    is_nan = torch.isnan(encoded_stock)
                    is_inf_pos = torch.isinf(encoded_stock) & (encoded_stock > 0)
                    is_inf_neg = torch.isinf(encoded_stock) & (encoded_stock < 0)
                    
                    encoded_stock = torch.where(is_nan, torch.zeros_like(encoded_stock), encoded_stock)
                    encoded_stock = torch.where(is_inf_pos, torch.ones_like(encoded_stock) * 1e6, encoded_stock)
                    encoded_stock = torch.where(is_inf_neg, torch.ones_like(encoded_stock) * -1e6, encoded_stock)
                    
                    if not self.training:  # 只在评估模式下打印
                        self.logger.warning("警告: 位置编码后包含NaN或Inf值，已替换")
                
                encoded_features[:, :, i, :] = encoded_stock
            
            # 3. Transformer编码器处理时间序列
            # 对每只股票分别应用Transformer
            transformer_features = torch.zeros_like(encoded_features)
            for i in range(num_stocks):
                # 获取单只股票的序列: [batch_size, seq_len, d_model]
                stock_seq = encoded_features[:, :, i, :]
                
                # 转换维度为Transformer的输入格式: [seq_len, batch_size, d_model]
                stock_seq = stock_seq.permute(1, 0, 2)
                
                # 应用Transformer编码器
                encoded_stock_seq = self.transformer_encoder(stock_seq)
                
                # 检查Transformer编码后是否有NaN/Inf
                if torch.isnan(encoded_stock_seq).any() or torch.isinf(encoded_stock_seq).any():
                    is_nan = torch.isnan(encoded_stock_seq)
                    is_inf_pos = torch.isinf(encoded_stock_seq) & (encoded_stock_seq > 0)
                    is_inf_neg = torch.isinf(encoded_stock_seq) & (encoded_stock_seq < 0)
                    
                    encoded_stock_seq = torch.where(is_nan, torch.zeros_like(encoded_stock_seq), encoded_stock_seq)
                    encoded_stock_seq = torch.where(is_inf_pos, torch.ones_like(encoded_stock_seq) * 1e6, encoded_stock_seq)
                    encoded_stock_seq = torch.where(is_inf_neg, torch.ones_like(encoded_stock_seq) * -1e6, encoded_stock_seq)
                    
                    if not self.training:  # 只在评估模式下打印
                        self.logger.warning("警告: Transformer编码后包含NaN或Inf值，已替换")
                
                # 转换回原始维度: [batch_size, seq_len, d_model]
                encoded_stock_seq = encoded_stock_seq.permute(1, 0, 2)
                
                # 保存编码结果
                transformer_features[:, :, i, :] = encoded_stock_seq
            
            # 4. 使用最后一个时间步的特征
            last_step_features = transformer_features[:, -1, :, :]  # [batch_size, num_stocks, d_model]
            
            # 5. 跨资产注意力网络
            # 准备输入CAAN的特征
            caan_input = last_step_features.clone()  # [batch_size, num_stocks, d_model]
            
            # 应用CAAN
            cross_attended_features, attention_weights = self.caan(caan_input)
            
            # 检查CAAN输出是否有NaN/Inf
            if torch.isnan(cross_attended_features).any() or torch.isinf(cross_attended_features).any():
                is_nan = torch.isnan(cross_attended_features)
                is_inf_pos = torch.isinf(cross_attended_features) & (cross_attended_features > 0)
                is_inf_neg = torch.isinf(cross_attended_features) & (cross_attended_features < 0)
                
                cross_attended_features = torch.where(is_nan, torch.zeros_like(cross_attended_features), cross_attended_features)
                cross_attended_features = torch.where(is_inf_pos, torch.ones_like(cross_attended_features) * 1e6, cross_attended_features)
                cross_attended_features = torch.where(is_inf_neg, torch.ones_like(cross_attended_features) * -1e6, cross_attended_features)
                
                if not self.training:  # 只在评估模式下打印
                    self.logger.warning("警告: CAAN输出包含NaN或Inf值，已替换")
            
            # 6. 投资组合生成网络
            flattened_features = cross_attended_features.reshape(-1, self.d_model)
            weights_logits = self.portfolio_generator(flattened_features)
            try:
                weights_logits = weights_logits.reshape(batch_size, num_stocks)
            except RuntimeError as e:
                self.logger.error(f"重塑权重logits失败: 输入形状={weights_logits.shape}, 目标形状=[{batch_size}, {num_stocks}]")
                weights_logits = torch.ones(batch_size, num_stocks, device=weights_logits.device) / num_stocks

            # 检查权重logits是否有NaN/Inf
            if torch.isnan(weights_logits).any() or torch.isinf(weights_logits).any():
                is_nan = torch.isnan(weights_logits)
                is_inf_pos = torch.isinf(weights_logits) & (weights_logits > 0)
                is_inf_neg = torch.isinf(weights_logits) & (weights_logits < 0)
                weights_logits = torch.where(is_nan, torch.zeros_like(weights_logits), weights_logits)
                weights_logits = torch.where(is_inf_pos, torch.ones_like(weights_logits) * self.weight_clipvalue, weights_logits)
                weights_logits = torch.where(is_inf_neg, torch.ones_like(weights_logits) * -self.weight_clipvalue, weights_logits)
                if not self.training:  # 只在评估模式下打印
                    self.logger.warning("警告: 权重logits包含NaN或Inf值，已替换")

            # 应用权重裁剪确保稳定性
            weights_logits = torch.clamp(weights_logits, -self.weight_clipvalue, self.weight_clipvalue)

            # G参数法
            G = min(self.G, num_stocks // 2)  # 防止G过大
            sorted_indices = torch.argsort(weights_logits, dim=1, descending=True)
            weights = torch.zeros_like(weights_logits)
            for b in range(batch_size):
                idx = sorted_indices[b]
                # 多头
                long_idx = idx[:G]
                long_scores = weights_logits[b, long_idx]
                long_weights = torch.softmax(long_scores, dim=0)
                weights[b, long_idx] = long_weights
                # 空头
                short_idx = idx[-G:]
                short_scores = weights_logits[b, short_idx]
                short_weights = torch.softmax(-short_scores, dim=0)
                weights[b, short_idx] = -short_weights

            # 修正：将所有权重整体平移+0.5，再归一化，使所有权重为正且和为1
            weights = weights + 0.5
            weights = weights / weights.sum(dim=1, keepdim=True)

            # 检查最终权重是否有NaN/Inf
            if torch.isnan(weights).any() or torch.isinf(weights).any():
                # 对于包含NaN或Inf的权重，使用均匀分布
                uniform_weights = torch.ones_like(weights) / num_stocks
                is_invalid = torch.isnan(weights) | torch.isinf(weights)
                weights = torch.where(is_invalid, uniform_weights, weights)
                if not self.training:  # 只在评估模式下打印
                    self.logger.warning("警告: 最终权重包含NaN或Inf值，已替换为均匀分布")

            # 检查权重是否需要梯度（仅在训练时检查）
            if self.training and not weights.requires_grad:
                self.logger.warning("警告: 权重不需要梯度，这将阻止反向传播")

            # 返回投资组合权重和注意力权重（用于解释）
            return weights, attention_weights
            
        except Exception as e:
            self.logger.error(f"AlphaPortfolio前向传播错误: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            
            # 尝试获取输入形状
            try:
                input_shape = x.shape
                if len(input_shape) >= 3:
                    batch_size = input_shape[0] if len(input_shape) >= 4 else 1
                    num_stocks = input_shape[-2] if len(input_shape) >= 3 else 30  # 默认30只股票
                else:
                    # 处理异常情况
                    batch_size = 1
                    num_stocks = 30  # 默认30只股票
            except:
                # 完全无法获取输入形状时的回退
                batch_size = 1
                num_stocks = 30  # 默认30只股票
            
            # 创建均匀分布的权重和随机注意力权重
            device = x.device if hasattr(x, 'device') else torch.device('cpu')
            uniform_weights = torch.ones(batch_size, num_stocks, device=device) / num_stocks
            random_attention = torch.ones(batch_size, num_stocks, num_stocks, device=device) / num_stocks
            
            # 确保在训练模式下权重需要梯度
            if self.training and not uniform_weights.requires_grad:
                uniform_weights = uniform_weights.clone().detach().requires_grad_(True)
            
            return uniform_weights, random_attention

class DQNPortfolioManager(nn.Module):
    """基于DQN的投资组合管理器"""
    
    def __init__(self, state_dim, action_dim):
        super(DQNPortfolioManager, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(state_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, action_dim)
        )
        
    def forward(self, state):
        return self.network(state)
        
    def act(self, state, epsilon=0.0):
        if torch.rand(1) < epsilon:
            return torch.randint(0, self.action_dim, (1,))
        else:
            with torch.no_grad():
                return self.network(state).argmax().item() 