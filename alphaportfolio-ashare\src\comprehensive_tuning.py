"""
Comprehensive Model Tuning Script

This script provides a complete solution for:
1. Hyperparameter optimization with multiple seeds and configurations
2. Model performance tuning with advanced algorithms
3. Comprehensive monitoring and evaluation
4. Cross-validation and generalization testing
5. Support for multiple RL algorithms
"""

import os
import sys
import json
import logging
import argparse
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Tuple, Any
import torch
import warnings
warnings.filterwarnings('ignore')

# Import our modules
from data_processor import AShareDataProcessor
from models import AlphaPortfolio
from environment import PortfolioEnv
from enhanced_trainer import EnhancedPortfolioTrainer, create_enhanced_trainer
from hyperparameter_tuner import HyperparameterTuner, HyperparameterConfig, create_tuning_study
from advanced_monitor import AdvancedTrainingMonitor, create_monitor


class ComprehensiveTuningFramework:
    """Comprehensive tuning framework for RL models"""
    
    def __init__(self, 
                 config_file: str = None,
                 results_dir: str = None,
                 enable_distributed: bool = False):
        """
        Initialize the comprehensive tuning framework
        
        Args:
            config_file: Path to configuration file
            results_dir: Directory to save results
            enable_distributed: Whether to enable distributed optimization
        """
        self.config = self.load_config(config_file)
        self.results_dir = results_dir or os.path.join('comprehensive_tuning_results', 
                                                      datetime.now().strftime('%Y%m%d_%H%M%S'))
        os.makedirs(self.results_dir, exist_ok=True)
        
        self.enable_distributed = enable_distributed
        self.logger = self.setup_logging()
        
        # Initialize components
        self.data_processor = None
        self.best_models = {}
        self.evaluation_results = {}
        
    def load_config(self, config_file: str = None) -> Dict[str, Any]:
        """Load configuration from file or use defaults"""
        default_config = {
            "data": {
                "index_type": "hs300",
                "start_year": 2019,
                "end_year": 2023,
                "test_year": 2024,
                "top_stocks": 50,
                "use_core_features": True
            },
            "tuning": {
                "n_trials": 100,
                "timeout": 7200,  # 2 hours
                "n_jobs": 1,
                "cv_splits": 3,
                "seeds": [42, 123, 456, 789, 999]
            },
            "algorithms": {
                "ppo": {
                    "enabled": True,
                    "clip_epsilon": [0.1, 0.2, 0.3],
                    "entropy_coef": [0.01, 0.02, 0.05],
                    "gae_lambda": [0.9, 0.95, 0.99]
                },
                "a2c": {
                    "enabled": True,
                    "entropy_coef": [0.01, 0.02, 0.05],
                    "value_coef": [0.25, 0.5, 1.0]
                },
                "ddpg": {
                    "enabled": False,
                    "noise_std": [0.1, 0.2, 0.3],
                    "tau": [0.001, 0.005, 0.01]
                }
            },
            "model": {
                "d_model_range": [128, 256, 512],
                "num_heads_range": [2, 4, 8],
                "num_layers_range": [1, 2, 3, 4],
                "dropout_range": [0.0, 0.1, 0.2, 0.3]
            },
            "training": {
                "max_episodes": 500,
                "eval_frequency": 20,
                "early_stopping_patience": 50,
                "batch_size_range": [32, 64, 128],
                "learning_rate_range": [1e-5, 1e-3]
            },
            "evaluation": {
                "metrics": ["sharpe_ratio", "total_return", "max_drawdown", "win_rate", "volatility"],
                "primary_metric": "sharpe_ratio",
                "overfitting_threshold": 0.1,
                "generalization_threshold": 0.8
            }
        }
        
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r') as f:
                user_config = json.load(f)
            # Merge configurations
            self._deep_update(default_config, user_config)
        
        return default_config
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """Deep update dictionary"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def setup_logging(self) -> logging.Logger:
        """Setup comprehensive logging"""
        logger = logging.getLogger('comprehensive_tuning')
        logger.setLevel(logging.INFO)
        
        # File handler
        log_file = os.path.join(self.results_dir, 'comprehensive_tuning.log')
        file_handler = logging.FileHandler(log_file)
        file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(file_formatter)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter('%(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def initialize_data_processor(self):
        """Initialize data processor"""
        data_config = self.config['data']
        self.data_processor = AShareDataProcessor(
            index_type=data_config['index_type'],
            top_stocks=data_config.get('top_stocks'),
            feature_subset=None if not data_config.get('use_core_features') else None
        )
        
        if data_config.get('use_core_features'):
            self.data_processor.feature_subset = self.data_processor.get_core_features()
        
        self.logger.info(f"Data processor initialized for {data_config['index_type']}")
    
    def run_hyperparameter_optimization(self, algorithm: str) -> Dict[str, Any]:
        """Run hyperparameter optimization for specific algorithm"""
        self.logger.info(f"Starting hyperparameter optimization for {algorithm}")
        
        # Create algorithm-specific tuner
        tuner = create_tuning_study(
            data_processor=self.data_processor,
            study_name=f"{algorithm}_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            n_trials=self.config['tuning']['n_trials']
        )
        
        # Customize tuner for algorithm
        tuner.algorithm = algorithm
        tuner.algorithm_config = self.config['algorithms'][algorithm]
        
        # Run optimization
        results = tuner.run_optimization()
        
        # Save algorithm-specific results
        algo_results_dir = os.path.join(self.results_dir, f'{algorithm}_optimization')
        os.makedirs(algo_results_dir, exist_ok=True)
        
        with open(os.path.join(algo_results_dir, 'results.json'), 'w') as f:
            json.dump(results, f, indent=2)
        
        return results
    
    def evaluate_model_robustness(self, 
                                 model_config: HyperparameterConfig,
                                 algorithm: str,
                                 seeds: List[int]) -> Dict[str, Any]:
        """Evaluate model robustness across multiple seeds"""
        self.logger.info(f"Evaluating model robustness with {len(seeds)} seeds")
        
        seed_results = []
        
        for seed in seeds:
            self.logger.info(f"Evaluating with seed {seed}")
            
            # Set seed
            torch.manual_seed(seed)
            np.random.seed(seed)
            
            # Prepare data
            train_data = self.data_processor.prepare_training_data(
                start_year=self.config['data']['start_year'],
                end_year=self.config['data']['end_year']
            )
            
            test_data = self.data_processor.prepare_training_data(
                start_year=self.config['data']['test_year'],
                end_year=self.config['data']['test_year']
            )
            
            if train_data is None or test_data is None:
                self.logger.warning(f"Skipping seed {seed} due to data issues")
                continue
            
            # Create environments
            train_env = PortfolioEnv(
                data=train_data,
                window_size=model_config.window_size,
                transaction_cost_pct=model_config.transaction_cost_pct
            )
            
            test_env = PortfolioEnv(
                data=test_data,
                window_size=model_config.window_size,
                transaction_cost_pct=model_config.transaction_cost_pct
            )
            
            # Create model
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model = AlphaPortfolio(
                input_dim=train_env.num_features,
                d_model=model_config.d_model,
                num_heads=model_config.num_heads,
                d_ff=model_config.d_ff,
                num_layers=model_config.num_layers,
                dropout=model_config.dropout,
                G=model_config.G
            ).to(device)
            
            # Create monitor
            monitor = create_monitor(
                save_dir=os.path.join(self.results_dir, f'monitoring_{algorithm}_seed_{seed}'),
                enable_tensorboard=True
            )
            
            # Create trainer
            trainer = create_enhanced_trainer(
                env=train_env,
                model=model,
                device=device,
                algorithm=algorithm,
                learning_rate=model_config.learning_rate,
                gamma=model_config.gamma,
                batch_size=model_config.batch_size,
                monitor=monitor
            )
            
            # Train model
            training_history = trainer.train(
                env=train_env,
                num_episodes=model_config.num_episodes,
                eval_frequency=model_config.eval_frequency,
                early_stopping_patience=model_config.early_stopping_patience
            )
            
            # Evaluate on test set
            test_metrics = trainer.evaluate(test_env, num_episodes=10)
            
            seed_result = {
                'seed': seed,
                'test_metrics': test_metrics,
                'training_episodes': len(training_history),
                'final_train_reward': training_history['reward'].iloc[-1] if not training_history.empty else 0.0,
                'convergence_episode': self.find_convergence_episode(training_history)
            }
            
            seed_results.append(seed_result)
            
            # Clean up
            monitor.close()
        
        # Analyze robustness
        robustness_analysis = self.analyze_robustness(seed_results)
        
        return {
            'seed_results': seed_results,
            'robustness_analysis': robustness_analysis
        }
    
    def find_convergence_episode(self, training_history: pd.DataFrame) -> int:
        """Find convergence episode from training history"""
        if training_history.empty or 'reward' not in training_history.columns:
            return -1
        
        rewards = training_history['reward'].values
        if len(rewards) < 20:
            return -1
        
        # Look for stability in recent episodes
        window_size = min(20, len(rewards) // 4)
        
        for i in range(window_size, len(rewards)):
            recent_rewards = rewards[i-window_size:i]
            if np.std(recent_rewards) < 0.01:  # Stable threshold
                return i
        
        return len(rewards)
    
    def analyze_robustness(self, seed_results: List[Dict]) -> Dict[str, Any]:
        """Analyze robustness across seeds"""
        if not seed_results:
            return {}
        
        # Extract metrics
        sharpe_ratios = [r['test_metrics']['avg_sharpe'] for r in seed_results]
        total_returns = [r['test_metrics']['total_return'] for r in seed_results]
        max_drawdowns = [r['test_metrics']['max_drawdown'] for r in seed_results]
        
        analysis = {
            'n_seeds': len(seed_results),
            'sharpe_ratio': {
                'mean': np.mean(sharpe_ratios),
                'std': np.std(sharpe_ratios),
                'min': np.min(sharpe_ratios),
                'max': np.max(sharpe_ratios),
                'cv': np.std(sharpe_ratios) / (np.abs(np.mean(sharpe_ratios)) + 1e-8)
            },
            'total_return': {
                'mean': np.mean(total_returns),
                'std': np.std(total_returns),
                'min': np.min(total_returns),
                'max': np.max(total_returns)
            },
            'max_drawdown': {
                'mean': np.mean(max_drawdowns),
                'std': np.std(max_drawdowns),
                'min': np.min(max_drawdowns),
                'max': np.max(max_drawdowns)
            },
            'consistency_score': 1.0 / (1.0 + np.std(sharpe_ratios))  # Higher is more consistent
        }
        
        return analysis
    
    def run_comprehensive_evaluation(self) -> Dict[str, Any]:
        """Run comprehensive evaluation of all algorithms"""
        self.logger.info("Starting comprehensive evaluation")
        
        if self.data_processor is None:
            self.initialize_data_processor()
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'config': self.config,
            'algorithms': {}
        }
        
        # Evaluate each enabled algorithm
        for algorithm, algo_config in self.config['algorithms'].items():
            if not algo_config.get('enabled', False):
                self.logger.info(f"Skipping disabled algorithm: {algorithm}")
                continue
            
            self.logger.info(f"Evaluating algorithm: {algorithm}")
            
            try:
                # Run hyperparameter optimization
                optimization_results = self.run_hyperparameter_optimization(algorithm)
                
                # Get best configuration
                best_config = HyperparameterConfig(**optimization_results['best_config'])
                
                # Evaluate robustness
                robustness_results = self.evaluate_model_robustness(
                    best_config, 
                    algorithm, 
                    self.config['tuning']['seeds']
                )
                
                # Store results
                results['algorithms'][algorithm] = {
                    'optimization': optimization_results,
                    'robustness': robustness_results,
                    'best_config': optimization_results['best_config']
                }
                
                self.logger.info(f"Completed evaluation for {algorithm}")
                
            except Exception as e:
                self.logger.error(f"Error evaluating {algorithm}: {str(e)}")
                results['algorithms'][algorithm] = {'error': str(e)}
        
        # Save comprehensive results
        results_file = os.path.join(self.results_dir, 'comprehensive_results.json')
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Generate summary report
        self.generate_summary_report(results)
        
        return results
    
    def generate_summary_report(self, results: Dict[str, Any]):
        """Generate comprehensive summary report"""
        report_lines = [
            "=" * 100,
            "COMPREHENSIVE MODEL TUNING REPORT",
            "=" * 100,
            f"Generated: {results['timestamp']}",
            f"Data: {results['config']['data']['index_type']} ({results['config']['data']['start_year']}-{results['config']['data']['end_year']})",
            f"Test Year: {results['config']['data']['test_year']}",
            "",
            "ALGORITHM PERFORMANCE SUMMARY",
            "=" * 50
        ]
        
        # Algorithm comparison
        algo_summary = []
        
        for algorithm, algo_results in results['algorithms'].items():
            if 'error' in algo_results:
                report_lines.append(f"{algorithm.upper()}: FAILED - {algo_results['error']}")
                continue
            
            robustness = algo_results['robustness']['robustness_analysis']
            
            summary = {
                'algorithm': algorithm,
                'mean_sharpe': robustness['sharpe_ratio']['mean'],
                'sharpe_std': robustness['sharpe_ratio']['std'],
                'consistency': robustness['consistency_score'],
                'mean_return': robustness['total_return']['mean'],
                'n_seeds': robustness['n_seeds']
            }
            
            algo_summary.append(summary)
            
            report_lines.extend([
                f"{algorithm.upper()}:",
                f"  Mean Sharpe Ratio: {summary['mean_sharpe']:.4f} (±{summary['sharpe_std']:.4f})",
                f"  Mean Total Return: {summary['mean_return']:.4f}",
                f"  Consistency Score: {summary['consistency']:.4f}",
                f"  Seeds Evaluated: {summary['n_seeds']}",
                ""
            ])
        
        # Best algorithm
        if algo_summary:
            best_algo = max(algo_summary, key=lambda x: x['mean_sharpe'])
            report_lines.extend([
                "BEST ALGORITHM",
                "=" * 30,
                f"Algorithm: {best_algo['algorithm'].upper()}",
                f"Mean Sharpe Ratio: {best_algo['mean_sharpe']:.4f}",
                f"Consistency Score: {best_algo['consistency']:.4f}",
                ""
            ])
        
        # Recommendations
        report_lines.extend([
            "RECOMMENDATIONS",
            "=" * 30
        ])
        
        if algo_summary:
            # Stability recommendation
            most_stable = max(algo_summary, key=lambda x: x['consistency'])
            report_lines.append(f"Most Stable Algorithm: {most_stable['algorithm'].upper()} (consistency: {most_stable['consistency']:.4f})")
            
            # Performance recommendation
            best_performance = max(algo_summary, key=lambda x: x['mean_sharpe'])
            report_lines.append(f"Best Performance: {best_performance['algorithm'].upper()} (Sharpe: {best_performance['mean_sharpe']:.4f})")
            
            # Risk-adjusted recommendation
            risk_adjusted_scores = []
            for summary in algo_summary:
                # Risk-adjusted score: performance / volatility
                risk_adj_score = summary['mean_sharpe'] * summary['consistency']
                risk_adjusted_scores.append((summary['algorithm'], risk_adj_score))
            
            best_risk_adj = max(risk_adjusted_scores, key=lambda x: x[1])
            report_lines.append(f"Best Risk-Adjusted: {best_risk_adj[0].upper()} (score: {best_risk_adj[1]:.4f})")
        
        report_lines.extend([
            "",
            "TUNING GUIDELINES",
            "=" * 30,
            "1. Use the best-performing algorithm for maximum returns",
            "2. Use the most stable algorithm for consistent performance",
            "3. Consider risk-adjusted performance for balanced approach",
            "4. Monitor training stability and convergence",
            "5. Validate on out-of-sample data regularly",
            "",
            "=" * 100
        ])
        
        # Save report
        report_text = "\n".join(report_lines)
        report_file = os.path.join(self.results_dir, 'comprehensive_report.txt')
        
        with open(report_file, 'w') as f:
            f.write(report_text)
        
        # Print summary to console
        print(report_text)
        
        self.logger.info(f"Comprehensive report saved to {report_file}")
    
    def get_tuning_recommendations(self) -> Dict[str, Any]:
        """Get tuning recommendations based on results"""
        results_file = os.path.join(self.results_dir, 'comprehensive_results.json')
        
        if not os.path.exists(results_file):
            return {"error": "No results found. Run comprehensive evaluation first."}
        
        with open(results_file, 'r') as f:
            results = json.load(f)
        
        recommendations = {
            'timestamp': datetime.now().isoformat(),
            'data_recommendations': {
                'feature_engineering': [
                    "Consider adding more technical indicators",
                    "Experiment with different lookback windows",
                    "Try feature selection techniques"
                ],
                'data_quality': [
                    "Check for data leakage",
                    "Validate data consistency",
                    "Consider data augmentation"
                ]
            },
            'model_recommendations': {},
            'training_recommendations': {
                'general': [
                    "Use early stopping to prevent overfitting",
                    "Monitor training stability",
                    "Validate on multiple time periods"
                ]
            }
        }
        
        # Algorithm-specific recommendations
        for algorithm, algo_results in results.get('algorithms', {}).items():
            if 'error' in algo_results:
                continue
            
            robustness = algo_results['robustness']['robustness_analysis']
            
            algo_recommendations = []
            
            # Stability recommendations
            if robustness['consistency_score'] < 0.7:
                algo_recommendations.append("Consider reducing learning rate for better stability")
                algo_recommendations.append("Increase regularization (dropout, weight decay)")
            
            # Performance recommendations
            if robustness['sharpe_ratio']['mean'] < 1.0:
                algo_recommendations.append("Try different reward function components")
                algo_recommendations.append("Experiment with longer training episodes")
            
            # Variance recommendations
            if robustness['sharpe_ratio']['cv'] > 0.5:
                algo_recommendations.append("High variance detected - use ensemble methods")
                algo_recommendations.append("Consider more conservative hyperparameters")
            
            recommendations['model_recommendations'][algorithm] = algo_recommendations
        
        return recommendations


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Comprehensive Model Tuning Framework')
    
    parser.add_argument('--config', type=str, default=None,
                       help='Path to configuration file')
    parser.add_argument('--results_dir', type=str, default=None,
                       help='Directory to save results')
    parser.add_argument('--mode', type=str, default='full',
                       choices=['full', 'optimize', 'evaluate', 'report'],
                       help='Tuning mode')
    parser.add_argument('--algorithm', type=str, default=None,
                       choices=['ppo', 'a2c', 'ddpg'],
                       help='Specific algorithm to tune (for optimize mode)')
    parser.add_argument('--distributed', action='store_true',
                       help='Enable distributed optimization')
    
    return parser.parse_args()


def main():
    """Main function"""
    args = parse_arguments()
    
    # Create tuning framework
    framework = ComprehensiveTuningFramework(
        config_file=args.config,
        results_dir=args.results_dir,
        enable_distributed=args.distributed
    )
    
    if args.mode == 'full':
        # Run comprehensive evaluation
        results = framework.run_comprehensive_evaluation()
        
        # Get recommendations
        recommendations = framework.get_tuning_recommendations()
        
        print("\n" + "="*50)
        print("COMPREHENSIVE TUNING COMPLETED")
        print("="*50)
        print(f"Results saved to: {framework.results_dir}")
        print("Check the comprehensive_report.txt for detailed analysis")
        
    elif args.mode == 'optimize':
        # Run optimization for specific algorithm
        framework.initialize_data_processor()
        
        algorithm = args.algorithm or 'ppo'
        results = framework.run_hyperparameter_optimization(algorithm)
        
        print(f"\nOptimization completed for {algorithm}")
        print(f"Best score: {results['best_value']:.4f}")
        
    elif args.mode == 'evaluate':
        # Run evaluation only
        framework.initialize_data_processor()
        
        # Load existing results and evaluate
        print("Evaluation mode - implement specific evaluation logic")
        
    elif args.mode == 'report':
        # Generate report from existing results
        recommendations = framework.get_tuning_recommendations()
        
        print("\nTuning Recommendations:")
        print(json.dumps(recommendations, indent=2))


if __name__ == "__main__":
    main()
