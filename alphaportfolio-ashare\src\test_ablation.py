import warnings
warnings.filterwarnings("ignore")
import argparse
import torch
import os
import logging
from datetime import datetime
import json
import pandas as pd
import matplotlib.pyplot as plt
from data_processor import AShareDataProcessor
from environment import PortfolioEnv
from models import AlphaPortfolio, DoubleIdentity
from supervised_models import SupervisedAlphaPortfolio
from trainer import PortfolioTrainer

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='测试AlphaPortfolio模型')
    
    # 基本参数
    parser.add_argument('--index_type', type=str, default='hs300',
                      choices=['hs300', 'zz500', 'zz1000'],
                      help='指数类型')
    parser.add_argument('--test_year', type=int, required=True,
                      help='测试年份')
    
    # 模型参数
    parser.add_argument('--initial_balance', type=float, default=1000000.0,
                      help='初始资金')
    parser.add_argument('--transaction_cost_pct', type=float, default=0.001,
                      help='交易成本百分比')
    parser.add_argument('--window_size', type=int, default=50,
                      help='观察窗口大小')
    parser.add_argument('--batch_size', type=int, default=64,
                      help='批次大小')
    
    # 数据参数
    parser.add_argument('--top_stocks', type=int, default=None,
                      help='使用交易量最大的前N支股票，默认使用全部')
    parser.add_argument('--use_core_features', action='store_true',
                      help='仅使用核心特征子集')
    parser.add_argument('--feature_list', type=str, default=None,
                      help='要使用的特征列表，以逗号分隔，例如: "close,open,volume"')
    
    # 模型路径
    parser.add_argument('--model_dir', type=str, required=True,
                      help='模型目录路径，包含所有消融实验的模型文件')
    parser.add_argument('--device', type=str, default=None,
                      help='计算设备，例如: "cuda" 或 "cpu"')
    
    # 输出参数
    parser.add_argument('--output_dir', type=str, default='results/test_ablation',
                      help='测试结果输出目录')
    
    # 新增参数
    parser.add_argument('--G', type=int, default=10, help='多空组合的G参数，决定多头和空头各自的资产数量')
    
    return parser.parse_args()

def setup_logging(output_dir):
    """设置日志"""
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(output_dir, f'test_ablation_{timestamp}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)

def load_model(model_path, env, device, args):
    """加载模型"""
    checkpoint = torch.load(model_path, map_location=device)
    state_dict = checkpoint['model_state_dict'] if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint else checkpoint
    
    # 根据模型名称确定架构
    model_name = os.path.basename(model_path).split('.')[0]
    
    if 'supervised' in model_name:
        # 监督学习模型
        model = SupervisedAlphaPortfolio(
            input_dim=env.num_features,
            d_model=256,  # 使用与训练相同的模型维度
            num_heads=4,
            d_ff=1024,
            num_layers=2,
            dropout=0.1,
            eps=1e-8,
            weight_clipvalue=5.0
        ).to(device)
    else:
        # 从保存的模型中提取维度信息
        if 'feature_processor.0.weight' in state_dict:
            saved_input_dim = state_dict['feature_processor.0.weight'].shape[1]
            saved_hidden_dim = state_dict['feature_processor.0.weight'].shape[0]
            saved_output_dim = state_dict['feature_processor.8.weight'].shape[0]
        else:
            saved_input_dim = env.num_features
            saved_hidden_dim = 196
            saved_output_dim = 256
        
        model = AlphaPortfolio(
            input_dim=saved_input_dim,
            d_model=saved_output_dim,
            num_heads=4,
            d_ff=saved_output_dim * 4,
            num_layers=2,
            dropout=0.1,
            eps=1e-8,
            weight_clipvalue=5.0,
            G=args.G
        ).to(device)
        
        if 'te_only' in model_name:
            model.caan = DoubleIdentity()
    
    try:
        # 使用strict=False允许部分参数加载
        model.load_state_dict(state_dict, strict=False)
        logging.info(f"成功加载模型 {model_name} 的参数")
    except Exception as e:
        logging.warning(f"加载模型参数时出现警告: {str(e)}")
    
    model.eval()
    return model

def evaluate_model(model, env, device, batch_size=64):
    """评估模型"""
    trainer = PortfolioTrainer(
        env=env,
        model=model,
        device=device,
        batch_size=batch_size
    )
    
    # 评估模型
    metrics = trainer.evaluate(num_episodes=1)
    
    # 归一化投资组合价值
    if 'mean_portfolio_value' in metrics:
        metrics['mean_portfolio_value'] = metrics['mean_portfolio_value'] / env.initial_balance
    
    return metrics

def save_results(results, output_dir):
    """保存测试结果"""
    # 保存JSON结果
    results_file = os.path.join(output_dir, 'test_results.json')
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=4, ensure_ascii=False)
    
    # 绘制结果对比图
    df = pd.DataFrame(results).T
    
    for metric in df.columns:
        plt.figure(figsize=(10, 6))
        df[metric].plot(kind='bar')
        plt.title(f'{metric} Comparison')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'{metric}_comparison.png'))
        plt.close()

def main():
    """主函数"""
    args = parse_args()
    
    # 设置设备
    device = torch.device(args.device if args.device else ('cuda' if torch.cuda.is_available() else 'cpu'))
    
    # 设置日志
    logger = setup_logging(args.output_dir)
    logger.info("开始测试模型")
    logger.info(f"测试年份: {args.test_year}")
    logger.info(f"指数类型: {args.index_type}")
    
    # 准备测试数据
    logger.info("准备测试数据...")
    data_processor = AShareDataProcessor(
        index_type=args.index_type,
        top_stocks=args.top_stocks,
        feature_subset=args.feature_list.split(',') if args.feature_list else None
    )
    
    test_data = data_processor.prepare_test_data(args.test_year)
    if test_data is None:
        logger.error("无法准备测试数据")
        return
    
    # 创建测试环境
    env = PortfolioEnv(
        data=test_data,
        initial_balance=args.initial_balance,
        transaction_cost_pct=args.transaction_cost_pct,
        window_size=args.window_size
    )
    
    # 获取所有模型文件
    model_files = [f for f in os.listdir(args.model_dir) if f.endswith('.pth')]
    if not model_files:
        logger.error(f"在目录 {args.model_dir} 中没有找到模型文件")
        return
    
    # 测试结果
    results = {}
    
    # 测试每个模型
    for model_file in model_files:
        model_name = os.path.splitext(model_file)[0]
        model_path = os.path.join(args.model_dir, model_file)
        
        logger.info(f"\n测试模型: {model_name}")
        
        try:
            # 加载模型
            model = load_model(model_path, env, device, args)
            
            # 评估模型
            metrics = evaluate_model(model, env, device, args.batch_size)
            results[model_name] = metrics
            
            logger.info(f"模型 {model_name} 测试结果:")
            for metric_name, value in metrics.items():
                logger.info(f"  {metric_name}: {value:.4f}")
                
        except Exception as e:
            logger.error(f"测试模型 {model_name} 时出错: {str(e)}")
            continue
    
    # 保存测试结果
    save_results(results, args.output_dir)
    logger.info(f"\n测试结果已保存到: {args.output_dir}")

if __name__ == '__main__':
    main() 