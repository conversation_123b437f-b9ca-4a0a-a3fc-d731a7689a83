import torch
import logging
import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple
from models import AlphaPortfolio
from environment import PortfolioEnv
from trainer import PortfolioTrainer
from data_processor import AShareDataProcessor
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
import torch.nn as nn
from supervised_models import SupervisedAlphaPortfolio

class DoubleIdentity(nn.Module):
    """返回两个相同输入的 Identity 模块"""
    def forward(self, x):
        return x, torch.zeros_like(x)  # 返回输入和相同形状的零张量

class SingleIdentity(nn.Module):
    """返回单个输入的 Identity 模块"""
    def forward(self, x):
        return x  # 只返回输入

class AblationStudy:
    """消融实验研究类"""
    
    def __init__(self, 
                 index_type: str = 'hs300',
                 start_year: int = 2010,
                 end_year: int = 2020,
                 initial_balance: float = 1000000.0,
                 transaction_cost_pct: float = 0.001,
                 window_size: int = 50,
                 batch_size: int = 64,
                 learning_rate: float = 0.0001,
                 gamma: float = 0.99,
                 top_stocks: Optional[int] = None,
                 use_core_features: bool = False,
                 feature_list: Optional[str] = None,
                 num_episodes: int = 1000,
                 device: Optional[torch.device] = None,
                 G: int = 10):
        """
        初始化消融实验研究
        
        Args:
            index_type: 指数类型
            start_year: 起始年份
            end_year: 结束年份
            initial_balance: 初始资金
            transaction_cost_pct: 交易成本百分比
            window_size: 观察窗口大小
            batch_size: 批次大小
            learning_rate: 学习率
            gamma: 折扣因子
            top_stocks: 使用交易量最大的前N支股票，默认使用全部
            use_core_features: 是否仅使用核心特征子集
            feature_list: 要使用的特征列表，以逗号分隔，例如: "close,open,volume"
            num_episodes: 训练回合数
            device: 计算设备
            G: 多空组合的G参数，决定多头和空头各自的资产数量
        """
        self.index_type = index_type
        self.start_year = start_year
        self.end_year = end_year
        self.initial_balance = initial_balance
        self.transaction_cost_pct = transaction_cost_pct
        self.window_size = window_size
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.gamma = gamma
        self.top_stocks = top_stocks
        self.use_core_features = use_core_features
        self.feature_list = feature_list
        self.num_episodes = num_episodes
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.G = G
        
        # 创建环境
        data_processor = AShareDataProcessor(
            index_type=self.index_type,
            top_stocks=self.top_stocks,
            feature_subset=self.feature_list if self.feature_list else None
        )
        data = data_processor.prepare_training_data(
            start_year=self.start_year,
            end_year=self.end_year
        )
        self.env = PortfolioEnv(
            data=data,
            initial_balance=self.initial_balance,
            transaction_cost_pct=self.transaction_cost_pct,
            window_size=self.window_size
        )
        
        # 创建结果保存目录
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.results_dir = f'results/ablation_{self.timestamp}'
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 创建模型保存目录
        self.models_dir = os.path.join(self.results_dir, 'models')
        os.makedirs(self.models_dir, exist_ok=True)
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        self.setup_logging()
        
        # 记录实验参数
        self.logger.info("实验参数:")
        self.logger.info(f"  index_type: {index_type}")
        self.logger.info(f"  start_year: {start_year}")
        self.logger.info(f"  end_year: {end_year}")
        self.logger.info(f"  top_stocks: {top_stocks}")
        self.logger.info(f"  use_core_features: {use_core_features}")
        self.logger.info(f"  feature_list: {feature_list}")
        self.logger.info(f"  num_episodes: {num_episodes}")
        self.logger.info(f"  G: {G}")
        
    def setup_logging(self):
        """设置日志"""
        log_file = os.path.join(self.results_dir, 'ablation_study.log')
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        self.logger.setLevel(logging.INFO)
        
    def prepare_data(self, feature_subset: Optional[List[str]] = None) -> Tuple[pd.DataFrame, PortfolioEnv]:
        """准备数据"""
        self.logger.info("准备数据...")
        
        # 确定要使用的特征
        if self.feature_list:
            # 使用用户指定的特征列表
            features = [f.strip() for f in self.feature_list.split(',')]
            self.logger.info(f"使用用户指定特征: {features}")
            feature_subset = features
        elif self.use_core_features:
            # 使用核心特征子集
            data_processor = AShareDataProcessor(index_type=self.index_type)
            feature_subset = data_processor.get_core_features()
            self.logger.info(f"使用核心特征子集: {feature_subset}")
        elif feature_subset is not None:
            # 使用传入的特征子集
            self.logger.info(f"使用传入的特征子集: {feature_subset}")
        
        data_processor = AShareDataProcessor(
            index_type=self.index_type,
            top_stocks=self.top_stocks,
            feature_subset=feature_subset
        )
        data = data_processor.prepare_training_data(
            start_year=self.start_year,
            end_year=self.end_year
        )
        
        env = PortfolioEnv(
            data=data,
            initial_balance=self.initial_balance,
            transaction_cost_pct=self.transaction_cost_pct,
            window_size=self.window_size
        )
        
        return data, env
        
    def train_and_save_model(self, model, model_name: str, trainer: PortfolioTrainer):
        """训练并保存模型"""
        self.logger.info(f"开始训练模型: {model_name}")
        
        # 训练模型
        trainer.train(num_episodes=self.num_episodes)
        
        # 保存模型
        model_path = os.path.join(self.models_dir, f"{model_name}.pth")
        trainer.save_model(model_path)
        self.logger.info(f"模型已保存到: {model_path}")
        
        return model_path
        
    def load_and_evaluate_model(self, model_path: str, model_name: str, trainer: PortfolioTrainer):
        """加载并评估模型"""
        self.logger.info(f"加载模型: {model_name}")
        
        # 加载模型
        trainer.load_model(model_path)
        
        # 评估模型
        metrics = trainer.evaluate(num_episodes=self.num_episodes)
        self.logger.info(f"模型 {model_name} 评估结果: {metrics}")
        
        return metrics
        
    def run_model_architecture_ablation(self):
        """模型架构消融实验"""
        print("\n=== 模型架构消融实验 ===")
        results = {}
        
        # 1. 完整模型
        print("\n1. 完整模型")
        model_full = AlphaPortfolio(
            input_dim=self.env.num_features,
            d_model=256,
            num_heads=4,
            d_ff=1024,
            num_layers=2,
            dropout=0.1,
            eps=1e-8,
            weight_clipvalue=5.0,
            G=self.G
        ).to(self.device)
        
        trainer = PortfolioTrainer(
            env=self.env,
            model=model_full,
            device=self.device,
            learning_rate=self.learning_rate,
            gamma=self.gamma,
            batch_size=self.batch_size
        )
        
        # 训练并保存模型
        model_path = self.train_and_save_model(model_full, "architecture_full_model", trainer)
        
        # 加载并评估模型
        metrics = self.load_and_evaluate_model(model_path, "architecture_full_model", trainer)
        results['full_model'] = metrics
        
        # 2. Transformer Encoder (TE)
        print("\n2. Transformer Encoder")
        model_te = AlphaPortfolio(
            input_dim=self.env.num_features,
            d_model=256,
            num_heads=4,
            d_ff=1024,
            num_layers=2,
            dropout=0.1,
            eps=1e-8,
            weight_clipvalue=5.0,
            G=self.G
        ).to(self.device)
        model_te.caan = DoubleIdentity()  # 使用 DoubleIdentity 替换 CAAN
        
        trainer = PortfolioTrainer(
            env=self.env,
            model=model_te,
            device=self.device,
            learning_rate=self.learning_rate,
            gamma=self.gamma,
            batch_size=self.batch_size
        )
        
        # 训练并保存模型
        model_path = self.train_and_save_model(model_te, "architecture_te_only", trainer)
        
        # 加载并评估模型
        metrics = self.load_and_evaluate_model(model_path, "architecture_te_only", trainer)
        results['te_only'] = metrics
        
        # 3. TE + CAAN
        print("\n3. Transformer Encoder + CAAN")
        model_te_caan = AlphaPortfolio(
            input_dim=self.env.num_features,
            d_model=256,
            num_heads=4,
            d_ff=1024,
            num_layers=2,
            dropout=0.1,
            eps=1e-8,
            weight_clipvalue=5.0,
            G=self.G
        ).to(self.device)
        
        trainer = PortfolioTrainer(
            env=self.env,
            model=model_te_caan,
            device=self.device,
            learning_rate=self.learning_rate,
            gamma=self.gamma,
            batch_size=self.batch_size
        )
        
        # 训练并保存模型
        model_path = self.train_and_save_model(model_te_caan, "architecture_te_caan", trainer)
        
        # 加载并评估模型
        metrics = self.load_and_evaluate_model(model_path, "architecture_te_caan", trainer)
        results['te_caan'] = metrics
        
        # 保存结果
        self.save_results(results, 'model_architecture')
        
    def run_training_method_ablation(self):
        """训练方法消融实验"""
        print("\n=== 训练方法消融实验 ===")
        results = {}
        
        # 1. 深度强化学习 (DRL)
        print("\n1. 深度强化学习")
        model_drl = AlphaPortfolio(
            input_dim=self.env.num_features,
            d_model=256,
            num_heads=4,
            d_ff=1024,
            num_layers=2,
            dropout=0.1,
            eps=1e-8,
            weight_clipvalue=5.0,
            G=self.G
        ).to(self.device)
        
        trainer = PortfolioTrainer(
            env=self.env,
            model=model_drl,
            device=self.device,
            learning_rate=self.learning_rate,
            gamma=self.gamma,
            batch_size=self.batch_size
        )
        
        # 训练并保存模型
        model_path = self.train_and_save_model(model_drl, "training_drl", trainer)
        
        # 加载并评估模型
        metrics = self.load_and_evaluate_model(model_path, "training_drl", trainer)
        results['drl'] = metrics
        
        # 2. 监督学习
        print("\n2. 监督学习")
        model_sl = SupervisedAlphaPortfolio(
            input_dim=self.env.num_features,
            d_model=256,
            num_heads=4,
            d_ff=1024,
            num_layers=2,
            dropout=0.1,
            eps=1e-8,
            weight_clipvalue=5.0
        ).to(self.device)
        
        trainer = PortfolioTrainer(
            env=self.env,
            model=model_sl,
            device=self.device,
            learning_rate=self.learning_rate,
            gamma=self.gamma,
            batch_size=self.batch_size
        )
        
        # 训练并保存模型
        model_path = self.train_and_save_model(model_sl, "training_supervised", trainer)
        
        # 加载并评估模型
        metrics = self.load_and_evaluate_model(model_path, "training_supervised", trainer)
        results['supervised'] = metrics
        
        # 保存结果
        self.save_results(results, 'training_method')
        
    def run_feature_ablation(self):
        """特征消融实验"""
        self.logger.info("开始特征消融实验...")
        results = {}
        
        # 1. 所有特征
        data, env = self.prepare_data()
        model = AlphaPortfolio(input_dim=env.num_features).to(self.device)
        trainer = PortfolioTrainer(
            env=env,
            model=model,
            device=self.device,
            learning_rate=self.learning_rate,
            gamma=self.gamma,
            batch_size=self.batch_size
        )
        
        # 训练并保存模型
        model_path = self.train_and_save_model(model, "feature_all_features", trainer)
        
        # 加载并评估模型
        metrics = self.load_and_evaluate_model(model_path, "feature_all_features", trainer)
        results['all_features'] = metrics
        
        # 2. 核心特征子集
        if self.use_core_features:
            data_processor = AShareDataProcessor(index_type=self.index_type)
            core_features = data_processor.get_core_features()
            self.logger.info(f"使用核心特征子集: {core_features}")
        elif self.feature_list:
            core_features = [f.strip() for f in self.feature_list.split(',')]
            self.logger.info(f"使用用户指定特征: {core_features}")
        else:
            core_features = ['close', 'open', 'high', 'low', 'volume']
            self.logger.info(f"使用默认核心特征: {core_features}")
            
        data_core, env_core = self.prepare_data(feature_subset=core_features)
        model_core = AlphaPortfolio(input_dim=env_core.num_features).to(self.device)
        trainer = PortfolioTrainer(
            env=env_core,
            model=model_core,
            device=self.device,
            learning_rate=self.learning_rate,
            gamma=self.gamma,
            batch_size=self.batch_size
        )
        
        # 训练并保存模型
        model_path = self.train_and_save_model(model_core, "feature_core_features", trainer)
        
        # 加载并评估模型
        metrics = self.load_and_evaluate_model(model_path, "feature_core_features", trainer)
        results['core_features'] = metrics
        
        # 保存结果
        self.save_results(results, 'feature_ablation')
        
    def run_portfolio_ablation(self):
        """运行投资组合消融实验"""
        print("\n=== 投资组合消融实验 ===")
        results = {}
        
        # 1. 多头+空头组合
        print("\n1. 多头+空头组合")
        model_long_short = AlphaPortfolio(
            input_dim=self.env.num_features,
            d_model=256,
            num_heads=4,
            d_ff=1024,
            num_layers=2,
            dropout=0.1,
            eps=1e-8,
            weight_clipvalue=5.0,
            G=self.G
        ).to(self.device)
        
        trainer = PortfolioTrainer(
            env=self.env,
            model=model_long_short,
            device=self.device,
            learning_rate=self.learning_rate,
            gamma=self.gamma,
            batch_size=self.batch_size
        )
        
        # 训练并保存模型
        model_path = self.train_and_save_model(model_long_short, "portfolio_long_short", trainer)
        
        # 加载并评估模型
        metrics = self.load_and_evaluate_model(model_path, "portfolio_long_short", trainer)
        results['long_short'] = metrics
        
        # 2. 仅多头组合
        print("\n2. 仅多头组合")
        model_long = AlphaPortfolio(
            input_dim=self.env.num_features,
            d_model=256,
            num_heads=4,
            d_ff=1024,
            num_layers=2,
            dropout=0.1,
            eps=1e-8,
            weight_clipvalue=5.0,
            G=self.G
        ).to(self.device)
        
        # 在评估时只使用正权重
        model_long.portfolio_generator = nn.Sequential(
            model_long.portfolio_generator,
            nn.ReLU()  # 将负权重置为0
        )
        
        trainer = PortfolioTrainer(
            env=self.env,
            model=model_long,
            device=self.device,
            learning_rate=self.learning_rate,
            gamma=self.gamma,
            batch_size=self.batch_size
        )
        
        # 训练并保存模型
        model_path = self.train_and_save_model(model_long, "portfolio_long_only", trainer)
        
        # 加载并评估模型
        metrics = self.load_and_evaluate_model(model_path, "portfolio_long_only", trainer)
        results['long_only'] = metrics
        
        # 保存结果
        self.save_results(results, 'portfolio_ablation')
        
    def run_market_condition_ablation(self):
        """市场条件消融实验"""
        self.logger.info("开始市场条件消融实验...")
        results = {}
        
        # 1. 牛市环境
        data_bull, env_bull = self.prepare_data()
        model = AlphaPortfolio(input_dim=env_bull.num_features).to(self.device)
        trainer = PortfolioTrainer(
            env=env_bull,
            model=model,
            device=self.device,
            learning_rate=self.learning_rate,
            gamma=self.gamma,
            batch_size=self.batch_size
        )
        
        # 训练并保存模型
        model_path = self.train_and_save_model(model, "market_bull", trainer)
        
        # 加载并评估模型
        metrics = self.load_and_evaluate_model(model_path, "market_bull", trainer)
        results['bull_market'] = metrics
        
        # 2. 熊市环境
        data_bear, env_bear = self.prepare_data()
        model = AlphaPortfolio(input_dim=env_bear.num_features).to(self.device)
        trainer = PortfolioTrainer(
            env=env_bear,
            model=model,
            device=self.device,
            learning_rate=self.learning_rate,
            gamma=self.gamma,
            batch_size=self.batch_size
        )
        
        # 训练并保存模型
        model_path = self.train_and_save_model(model, "market_bear", trainer)
        
        # 加载并评估模型
        metrics = self.load_and_evaluate_model(model_path, "market_bear", trainer)
        results['bear_market'] = metrics
        
        # 保存结果
        self.save_results(results, 'market_condition')
        
    def run_economic_constraint_ablation(self):
        """经济约束消融实验"""
        self.logger.info("开始经济约束消融实验...")
        results = {}
        
        data, env = self.prepare_data()
        
        # 1. 包含交易成本
        model_with_cost = AlphaPortfolio(input_dim=env.num_features).to(self.device)
        trainer = PortfolioTrainer(
            env=env,
            model=model_with_cost,
            device=self.device,
            learning_rate=self.learning_rate,
            gamma=self.gamma,
            batch_size=self.batch_size
        )
        
        # 训练并保存模型
        model_path = self.train_and_save_model(model_with_cost, "economic_with_cost", trainer)
        
        # 加载并评估模型
        metrics = self.load_and_evaluate_model(model_path, "economic_with_cost", trainer)
        results['with_transaction_cost'] = metrics
        
        # 2. 忽略交易成本
        env_no_cost = PortfolioEnv(
            data=data,
            initial_balance=self.initial_balance,
            transaction_cost_pct=0.0,
            window_size=self.window_size
        )
        model_no_cost = AlphaPortfolio(input_dim=env_no_cost.num_features).to(self.device)
        trainer = PortfolioTrainer(
            env=env_no_cost,
            model=model_no_cost,
            device=self.device,
            learning_rate=self.learning_rate,
            gamma=self.gamma,
            batch_size=self.batch_size
        )
        
        # 训练并保存模型
        model_path = self.train_and_save_model(model_no_cost, "economic_no_cost", trainer)
        
        # 加载并评估模型
        metrics = self.load_and_evaluate_model(model_path, "economic_no_cost", trainer)
        results['no_transaction_cost'] = metrics
        
        # 保存结果
        self.save_results(results, 'economic_constraint')
        
    def save_results(self, results: Dict, experiment_name: str):
        """保存实验结果"""
        # 保存到CSV
        df = pd.DataFrame(results).T
        csv_path = os.path.join(self.results_dir, f'{experiment_name}_results.csv')
        df.to_csv(csv_path)
        
        # 绘制结果图表
        self.plot_results(results, experiment_name)
        
    def plot_results(self, results: Dict, experiment_name: str):
        """绘制实验结果图表"""
        plt.figure(figsize=(12, 8))
        
        # 提取关键指标
        metrics = ['mean_reward', 'mean_sharpe', 'mean_max_drawdown', 'mean_win_rate']
        for i, metric in enumerate(metrics, 1):
            plt.subplot(2, 2, i)
            values = [results[k][metric] for k in results.keys()]
            labels = list(results.keys())
            
            plt.bar(labels, values)
            plt.title(f'{metric}')
            plt.xticks(rotation=45)
            
        plt.tight_layout()
        plt.savefig(os.path.join(self.results_dir, f'{experiment_name}_results.png'))
        plt.close()
        
    def run_all_ablation_studies(self):
        """运行所有消融实验"""
        self.logger.info("开始运行所有消融实验...")
        
        # 运行各个消融实验
        self.run_model_architecture_ablation()
        self.run_training_method_ablation()
        self.run_feature_ablation()
        self.run_portfolio_ablation()
        self.run_market_condition_ablation()
        self.run_economic_constraint_ablation()
        
        self.logger.info("所有消融实验完成！")

if __name__ == '__main__':
    # 创建消融实验实例
    ablation = AblationStudy(
        index_type='hs300',
        start_year=2019,
        end_year=2019,
        initial_balance=1000000.0,
        transaction_cost_pct=0.001,
        window_size=10,
        batch_size=16,
        learning_rate=0.0001,
        gamma=0.99,
        top_stocks=5,  # 使用交易量最大的前5支股票
        use_core_features=True,  # 使用核心特征子集
        num_episodes=1000,  # 训练回合数
        G=10  # 多空组合的G参数，决定多头和空头各自的资产数量
    )
    
    # 运行所有消融实验
    ablation.run_all_ablation_studies() 