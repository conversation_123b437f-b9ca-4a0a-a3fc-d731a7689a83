import warnings
warnings.filterwarnings("ignore")
import argparse
import torch
from ablation_studies import AblationStudy

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='运行AlphaPortfolio消融实验')
    
    # 基本参数
    parser.add_argument('--index_type', type=str, default='hs300',
                      choices=['hs300', 'zz500', 'zz1000'],
                      help='指数类型')
    parser.add_argument('--start_year', type=int, required=True,
                      help='训练数据起始年份')
    parser.add_argument('--end_year', type=int, required=True,
                      help='训练数据结束年份')
    
    # 模型参数
    parser.add_argument('--initial_balance', type=float, default=1000000.0,
                      help='初始资金')
    parser.add_argument('--transaction_cost_pct', type=float, default=0.001,
                      help='交易成本百分比')
    parser.add_argument('--window_size', type=int, default=50,
                      help='观察窗口大小')
    parser.add_argument('--batch_size', type=int, default=64,
                      help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=0.0001,
                      help='学习率')
    parser.add_argument('--gamma', type=float, default=0.99,
                      help='折扣因子')
    
    # 数据参数
    parser.add_argument('--top_stocks', type=int, default=None,
                      help='使用交易量最大的前N支股票，默认使用全部')
    parser.add_argument('--use_core_features', action='store_true',
                      help='仅使用核心特征子集')
    parser.add_argument('--feature_list', type=str, default=None,
                      help='要使用的特征列表，以逗号分隔，例如: "close,open,volume"')
    
    # 训练参数
    parser.add_argument('--num_episodes', type=int, default=1000,
                      help='训练回合数')
    parser.add_argument('--device', type=str, default=None,
                      help='计算设备，例如: "cuda" 或 "cpu"')
    
    # 实验类型
    parser.add_argument('--experiment_type', type=str, required=True,
                      choices=['architecture', 'training', 'feature', 'portfolio', 
                              'market', 'economic', 'all'],
                      help='要运行的实验类型')
    
    # 新增参数
    parser.add_argument('--G', type=int, default=10,
                      help='多空组合的G参数，决定多头和空头各自的资产数量')
    
    return parser.parse_args()

def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 设置设备
    device = torch.device(args.device if args.device else ('cuda' if torch.cuda.is_available() else 'cpu'))
    
    # 创建消融实验实例
    ablation = AblationStudy(
        index_type=args.index_type,
        start_year=args.start_year,
        end_year=args.end_year,
        initial_balance=args.initial_balance,
        transaction_cost_pct=args.transaction_cost_pct,
        window_size=args.window_size,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate,
        gamma=args.gamma,
        top_stocks=args.top_stocks,
        use_core_features=args.use_core_features,
        feature_list=args.feature_list,
        num_episodes=args.num_episodes,
        G=args.G,
        device=device
    )
    
    # 根据实验类型运行相应的实验
    if args.experiment_type == 'architecture':
        ablation.run_model_architecture_ablation()
    elif args.experiment_type == 'training':
        ablation.run_training_method_ablation()
    elif args.experiment_type == 'feature':
        ablation.run_feature_ablation()
    elif args.experiment_type == 'portfolio':
        ablation.run_portfolio_ablation()
    elif args.experiment_type == 'market':
        ablation.run_market_condition_ablation()
    elif args.experiment_type == 'economic':
        ablation.run_economic_constraint_ablation()
    elif args.experiment_type == 'all':
        ablation.run_all_ablation_studies()

if __name__ == '__main__':
    main() 