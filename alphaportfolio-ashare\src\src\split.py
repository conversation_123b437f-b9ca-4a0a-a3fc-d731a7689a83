import pandas as pd
import os
from datetime import datetime

# === 设置路径 ===
input_file = "../dataset/hs300/hs300_rl_20250605/factors_hs300_20190101_20241231/factors_hs300_20190101_20241231.csv"  # 替换为你的实际路径
output_root = "split_by_year"  # 保存根目录

# === 创建保存目录 ===
os.makedirs(output_root, exist_ok=True)

# === 读取数据 ===
df = pd.read_csv(input_file)
df['trade_date'] = pd.to_datetime(df['trade_date'])

# === 获取所有年份 ===
years = sorted(df['trade_date'].dt.year.unique())

for year in years:
    # 过滤出当前年份数据
    df_year = df[df['trade_date'].dt.year == year].copy()

    if df_year.empty:
        continue

    # 获取最早和最晚日期
    start_date = df_year['trade_date'].min().strftime('%Y-%m-%d')
    end_date = df_year['trade_date'].max().strftime('%Y-%m-%d')
    start_fmt = df_year['trade_date'].min().strftime('%Y%m%d')
    end_fmt = df_year['trade_date'].max().strftime('%Y%m%d')

    # 构建文件夹和文件名
    folder_name = f"{start_date}_{end_date}"
    file_name = f"factors_hs300_{start_fmt}_{end_fmt}.csv"
    folder_path = os.path.join(output_root, folder_name)
    os.makedirs(folder_path, exist_ok=True)

    # 保存
    output_path = os.path.join(folder_path, file_name)
    df_year.to_csv(output_path, index=False)
    print(f"保存成功: {output_path}")
