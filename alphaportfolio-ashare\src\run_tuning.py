"""
Simple Usage Script for Comprehensive Model Tuning

This script provides easy-to-use commands for running different tuning modes.
"""

import os
import sys
import subprocess
from pathlib import Path

def run_comprehensive_tuning():
    """Run full comprehensive tuning with all algorithms"""
    print("🚀 Starting Comprehensive Model Tuning...")
    print("This will optimize hyperparameters for all enabled algorithms and evaluate robustness.")
    
    cmd = [
        sys.executable, "comprehensive_tuning.py",
        "--config", "tuning_config.json",
        "--mode", "full"
    ]
    
    subprocess.run(cmd)

def run_quick_optimization(algorithm="ppo"):
    """Run quick optimization for a single algorithm"""
    print(f"⚡ Running Quick Optimization for {algorithm.upper()}...")
    
    cmd = [
        sys.executable, "comprehensive_tuning.py",
        "--config", "tuning_config.json",
        "--mode", "optimize",
        "--algorithm", algorithm
    ]
    
    subprocess.run(cmd)

def run_basic_training():
    """Run basic training with default parameters"""
    print("📈 Running Basic Training...")
    
    cmd = [
        sys.executable, "main.py",
        "--index_type", "hs300",
        "--start_year", "2019",
        "--end_year", "2023",
        "--num_episodes", "100"
    ]
    
    subprocess.run(cmd)

def show_usage():
    """Show usage instructions"""
    print("""
🎯 AlphaPortfolio Model Tuning Usage Guide
==========================================

Available Commands:
1. Full Comprehensive Tuning (Recommended for production)
2. Quick Algorithm Optimization (Fast testing)
3. Basic Training (Simple baseline)
4. Show this help

Choose an option (1-4): """, end="")

def main():
    """Main interactive menu"""
    while True:
        show_usage()
        
        try:
            choice = input().strip()
            
            if choice == "1":
                run_comprehensive_tuning()
                break
            elif choice == "2":
                print("\nAvailable algorithms: ppo, a2c")
                algo = input("Enter algorithm (default: ppo): ").strip() or "ppo"
                run_quick_optimization(algo)
                break
            elif choice == "3":
                run_basic_training()
                break
            elif choice == "4":
                print("""
📚 Detailed Usage Instructions:

1. FULL COMPREHENSIVE TUNING:
   - Optimizes hyperparameters for all enabled algorithms
   - Tests robustness across multiple seeds
   - Generates detailed performance reports
   - Recommended for production model selection
   - Runtime: 2-4 hours depending on configuration

2. QUICK ALGORITHM OPTIMIZATION:
   - Fast optimization for a single algorithm
   - Good for testing and development
   - Runtime: 30-60 minutes

3. BASIC TRAINING:
   - Simple training with default parameters
   - Good for initial testing and debugging
   - Runtime: 10-20 minutes

Configuration:
- Edit tuning_config.json to customize parameters
- Adjust n_trials, timeout, and algorithm settings
- Enable/disable algorithms as needed

Results:
- All results saved in comprehensive_tuning_results/ directory
- Check comprehensive_report.txt for summary
- Individual algorithm results in separate subdirectories

Tips:
- Start with Quick Optimization for testing
- Use Full Comprehensive Tuning for final model selection
- Monitor GPU/CPU usage during optimization
- Check logs if training fails or gets stuck
                """)
                continue
            else:
                print("Invalid choice. Please enter 1-4.")
                continue
                
        except KeyboardInterrupt:
            print("\n\n👋 Tuning cancelled by user.")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            continue

if __name__ == "__main__":
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("🔧 AlphaPortfolio Model Tuning Framework")
    print("=" * 50)
    
    main()
