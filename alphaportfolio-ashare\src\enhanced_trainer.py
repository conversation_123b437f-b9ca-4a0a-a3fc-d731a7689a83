"""
Enhanced Portfolio Trainer with Advanced Features

This module provides an enhanced trainer with:
1. Generalized Advantage Estimation (GAE)
2. Improved reward functions
3. Advanced weight optimization
4. Integration with monitoring system
5. Multiple RL algorithm support
6. Adaptive learning strategies
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import pandas as pd
from collections import deque
import random
import os
import logging
from typing import List, Tuple, Dict, Any, Optional
from tqdm import tqdm
import time
from datetime import datetime
import quantstats as qs

from advanced_monitor import AdvancedTrainingMonitor


class GeneralizedAdvantageEstimator:
    """Generalized Advantage Estimation (GAE) implementation"""
    
    def __init__(self, gamma: float = 0.99, lambda_gae: float = 0.95):
        """
        Initialize GAE
        
        Args:
            gamma: Discount factor
            lambda_gae: GAE lambda parameter
        """
        self.gamma = gamma
        self.lambda_gae = lambda_gae
    
    def compute_advantages(self, 
                          rewards: np.ndarray, 
                          values: np.ndarray, 
                          next_values: np.ndarray,
                          dones: np.ndarray) -> <PERSON><PERSON>[np.ndarray, np.ndarray]:
        """
        Compute advantages using GAE
        
        Args:
            rewards: Array of rewards
            values: Array of value estimates
            next_values: Array of next state value estimates
            dones: Array of done flags
            
        Returns:
            Tuple of (advantages, returns)
        """
        advantages = np.zeros_like(rewards)
        returns = np.zeros_like(rewards)
        
        gae = 0
        for t in reversed(range(len(rewards))):
            if t == len(rewards) - 1:
                next_non_terminal = 1.0 - dones[t]
                next_value = next_values[t]
            else:
                next_non_terminal = 1.0 - dones[t]
                next_value = values[t + 1]
            
            delta = rewards[t] + self.gamma * next_value * next_non_terminal - values[t]
            gae = delta + self.gamma * self.lambda_gae * next_non_terminal * gae
            advantages[t] = gae
            returns[t] = advantages[t] + values[t]
        
        return advantages, returns


class AdvancedRewardFunction:
    """Advanced reward function with multiple components"""
    
    def __init__(self, 
                 sharpe_weight: float = 1.0,
                 return_weight: float = 0.5,
                 drawdown_weight: float = 0.3,
                 volatility_weight: float = 0.2,
                 turnover_weight: float = 0.1):
        """
        Initialize advanced reward function
        
        Args:
            sharpe_weight: Weight for Sharpe ratio component
            return_weight: Weight for return component
            drawdown_weight: Weight for drawdown penalty
            volatility_weight: Weight for volatility penalty
            turnover_weight: Weight for turnover penalty
        """
        self.sharpe_weight = sharpe_weight
        self.return_weight = return_weight
        self.drawdown_weight = drawdown_weight
        self.volatility_weight = volatility_weight
        self.turnover_weight = turnover_weight
        
        # Rolling statistics
        self.returns_history = deque(maxlen=252)  # 1 year
        self.portfolio_values = deque(maxlen=252)
        self.previous_weights = None
    
    def calculate_reward(self, 
                        portfolio_return: float,
                        portfolio_value: float,
                        current_weights: np.ndarray,
                        benchmark_return: float = 0.0) -> Dict[str, float]:
        """
        Calculate comprehensive reward
        
        Args:
            portfolio_return: Current portfolio return
            portfolio_value: Current portfolio value
            current_weights: Current portfolio weights
            benchmark_return: Benchmark return for comparison
            
        Returns:
            Dict with reward components
        """
        # Update history
        self.returns_history.append(portfolio_return)
        self.portfolio_values.append(portfolio_value)
        
        # Initialize components
        reward_components = {
            'total_reward': 0.0,
            'sharpe_component': 0.0,
            'return_component': 0.0,
            'drawdown_component': 0.0,
            'volatility_component': 0.0,
            'turnover_component': 0.0
        }
        
        if len(self.returns_history) < 10:
            reward_components['total_reward'] = portfolio_return
            reward_components['return_component'] = portfolio_return
            self.previous_weights = current_weights.copy()
            return reward_components
        
        returns_array = np.array(self.returns_history)
        values_array = np.array(self.portfolio_values)
        
        # 1. Sharpe ratio component
        if len(returns_array) >= 30:
            excess_returns = returns_array - benchmark_return
            if np.std(excess_returns) > 1e-8:
                sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns)
                reward_components['sharpe_component'] = sharpe_ratio * self.sharpe_weight
        
        # 2. Return component
        reward_components['return_component'] = portfolio_return * self.return_weight
        
        # 3. Drawdown penalty
        if len(values_array) >= 10:
            peak = np.maximum.accumulate(values_array)
            drawdown = (values_array - peak) / peak
            max_drawdown = np.min(drawdown)
            reward_components['drawdown_component'] = max_drawdown * self.drawdown_weight
        
        # 4. Volatility penalty
        if len(returns_array) >= 20:
            volatility = np.std(returns_array)
            reward_components['volatility_component'] = -volatility * self.volatility_weight
        
        # 5. Turnover penalty
        if self.previous_weights is not None:
            turnover = np.sum(np.abs(current_weights - self.previous_weights))
            reward_components['turnover_component'] = -turnover * self.turnover_weight
        
        # Total reward
        reward_components['total_reward'] = sum([
            reward_components['sharpe_component'],
            reward_components['return_component'],
            reward_components['drawdown_component'],
            reward_components['volatility_component'],
            reward_components['turnover_component']
        ])
        
        self.previous_weights = current_weights.copy()
        return reward_components


class AdaptiveLearningScheduler:
    """Adaptive learning rate scheduler based on training progress"""
    
    def __init__(self, 
                 initial_lr: float = 1e-4,
                 min_lr: float = 1e-6,
                 patience: int = 10,
                 factor: float = 0.5,
                 warmup_episodes: int = 50):
        """
        Initialize adaptive scheduler
        
        Args:
            initial_lr: Initial learning rate
            min_lr: Minimum learning rate
            patience: Episodes to wait before reducing LR
            factor: Factor to reduce LR by
            warmup_episodes: Episodes for warmup phase
        """
        self.initial_lr = initial_lr
        self.min_lr = min_lr
        self.patience = patience
        self.factor = factor
        self.warmup_episodes = warmup_episodes
        
        self.best_metric = float('-inf')
        self.episodes_without_improvement = 0
        self.current_lr = initial_lr
    
    def step(self, metric: float, episode: int, optimizer: torch.optim.Optimizer):
        """Update learning rate based on metric"""
        # Warmup phase
        if episode < self.warmup_episodes:
            warmup_lr = self.initial_lr * (episode + 1) / self.warmup_episodes
            for param_group in optimizer.param_groups:
                param_group['lr'] = warmup_lr
            self.current_lr = warmup_lr
            return
        
        # Check for improvement
        if metric > self.best_metric:
            self.best_metric = metric
            self.episodes_without_improvement = 0
        else:
            self.episodes_without_improvement += 1
        
        # Reduce learning rate if no improvement
        if self.episodes_without_improvement >= self.patience:
            new_lr = max(self.current_lr * self.factor, self.min_lr)
            if new_lr < self.current_lr:
                for param_group in optimizer.param_groups:
                    param_group['lr'] = new_lr
                self.current_lr = new_lr
                self.episodes_without_improvement = 0


class EnhancedPortfolioTrainer:
    """Enhanced portfolio trainer with advanced features"""
    
    def __init__(self,
                 env,
                 model,
                 device,
                 algorithm: str = 'ppo',
                 learning_rate: float = 1e-4,
                 gamma: float = 0.99,
                 batch_size: int = 64,
                 buffer_capacity: int = 10000,
                 gae_lambda: float = 0.95,
                 clip_epsilon: float = 0.2,
                 entropy_coef: float = 0.01,
                 value_coef: float = 0.5,
                 max_grad_norm: float = 0.5,
                 monitor: AdvancedTrainingMonitor = None,
                 timestamp: str = None):
        """
        Initialize enhanced trainer
        
        Args:
            env: Trading environment
            model: Neural network model
            device: Computation device
            algorithm: RL algorithm ('ppo', 'a2c', 'ddpg')
            learning_rate: Learning rate
            gamma: Discount factor
            batch_size: Batch size for training
            buffer_capacity: Experience buffer capacity
            gae_lambda: GAE lambda parameter
            clip_epsilon: PPO clipping parameter
            entropy_coef: Entropy coefficient
            value_coef: Value function coefficient
            max_grad_norm: Maximum gradient norm for clipping
            monitor: Training monitor
            timestamp: Timestamp for logging
        """
        self.env = env
        self.model = model
        self.device = device
        self.algorithm = algorithm
        self.gamma = gamma
        self.batch_size = batch_size
        self.timestamp = timestamp or datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Algorithm-specific parameters
        self.gae_lambda = gae_lambda
        self.clip_epsilon = clip_epsilon
        self.entropy_coef = entropy_coef
        self.value_coef = value_coef
        self.max_grad_norm = max_grad_norm
        
        # Initialize components
        self.gae = GeneralizedAdvantageEstimator(gamma, gae_lambda)
        self.reward_function = AdvancedRewardFunction()
        self.lr_scheduler = AdaptiveLearningScheduler(learning_rate)
        
        # Optimizer
        self.optimizer = optim.AdamW(
            model.parameters(),
            lr=learning_rate,
            weight_decay=0.01,
            eps=1e-8
        )
        
        # Experience buffer
        self.buffer = deque(maxlen=buffer_capacity)
        
        # Monitoring
        self.monitor = monitor
        self.logger = logging.getLogger(__name__)
        
        # Training statistics
        self.episode_rewards = []
        self.episode_losses = []
        self.episode_sharpe_ratios = []
        
        # Value network (for actor-critic methods)
        if algorithm in ['ppo', 'a2c']:
            self.value_network = nn.Sequential(
                nn.Linear(env.observation_space.shape[1] * env.observation_space.shape[2], 256),
                nn.ReLU(),
                nn.Linear(256, 128),
                nn.ReLU(),
                nn.Linear(128, 1)
            ).to(device)
            
            self.value_optimizer = optim.AdamW(
                self.value_network.parameters(),
                lr=learning_rate,
                weight_decay=0.01
            )
    
    def select_action(self, state: np.ndarray, training: bool = True) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        Select action using the current policy
        
        Args:
            state: Current state
            training: Whether in training mode
            
        Returns:
            Tuple of (action, info_dict)
        """
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            if hasattr(self.model, 'forward'):
                action_probs, attention = self.model(state_tensor)
            else:
                action_probs = self.model(state_tensor)
                attention = None
            
            # Convert to action
            if self.algorithm == 'ddpg':
                # Deterministic policy
                action = action_probs.cpu().numpy()[0]
            else:
                # Stochastic policy
                if training:
                    # Sample from distribution
                    dist = torch.distributions.Categorical(action_probs)
                    action_idx = dist.sample()
                    action = F.softmax(action_probs, dim=-1).cpu().numpy()[0]
                else:
                    # Greedy action
                    action = F.softmax(action_probs, dim=-1).cpu().numpy()[0]
        
        info = {
            'action_probs': action_probs.cpu().numpy()[0] if isinstance(action_probs, torch.Tensor) else action_probs,
            'attention': attention.cpu().numpy()[0] if attention is not None else None
        }
        
        return action, info
    
    def compute_value(self, state: np.ndarray) -> float:
        """Compute value estimate for state"""
        if not hasattr(self, 'value_network'):
            return 0.0
        
        state_flat = torch.FloatTensor(state.flatten()).unsqueeze(0).to(self.device)
        with torch.no_grad():
            value = self.value_network(state_flat).item()
        return value
    
    def store_transition(self, 
                        state: np.ndarray,
                        action: np.ndarray,
                        reward: float,
                        next_state: np.ndarray,
                        done: bool,
                        info: Dict[str, Any]):
        """Store transition in experience buffer"""
        transition = {
            'state': state,
            'action': action,
            'reward': reward,
            'next_state': next_state,
            'done': done,
            'value': self.compute_value(state),
            'info': info
        }
        self.buffer.append(transition)
    
    def compute_ppo_loss(self, batch: List[Dict]) -> Dict[str, torch.Tensor]:
        """Compute PPO loss"""
        states = torch.FloatTensor(np.array([t['state'] for t in batch])).to(self.device)
        actions = torch.FloatTensor(np.array([t['action'] for t in batch])).to(self.device)
        rewards = np.array([t['reward'] for t in batch])
        dones = np.array([t['done'] for t in batch])
        old_values = np.array([t['value'] for t in batch])
        
        # Compute advantages using GAE
        next_values = np.roll(old_values, -1)
        next_values[-1] = 0.0  # Terminal state
        
        advantages, returns = self.gae.compute_advantages(rewards, old_values, next_values, dones)
        
        # Normalize advantages
        advantages = (advantages - np.mean(advantages)) / (np.std(advantages) + 1e-8)
        
        # Convert to tensors
        advantages = torch.FloatTensor(advantages).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)
        
        # Forward pass
        action_probs, _ = self.model(states)
        values = self.value_network(states.view(states.size(0), -1)).squeeze()
        
        # Policy loss
        dist = torch.distributions.Categorical(F.softmax(action_probs, dim=-1))
        action_indices = torch.argmax(actions, dim=-1)
        log_probs = dist.log_prob(action_indices)
        
        # PPO clipped loss
        ratio = torch.exp(log_probs - log_probs.detach())
        clipped_ratio = torch.clamp(ratio, 1 - self.clip_epsilon, 1 + self.clip_epsilon)
        policy_loss = -torch.min(ratio * advantages, clipped_ratio * advantages).mean()
        
        # Value loss
        value_loss = F.mse_loss(values, returns)
        
        # Entropy loss
        entropy_loss = -dist.entropy().mean()
        
        # Total loss
        total_loss = policy_loss + self.value_coef * value_loss + self.entropy_coef * entropy_loss
        
        return {
            'total_loss': total_loss,
            'policy_loss': policy_loss,
            'value_loss': value_loss,
            'entropy_loss': entropy_loss
        }
    
    def update_model(self) -> Dict[str, float]:
        """Update model parameters"""
        if len(self.buffer) < self.batch_size:
            return {'loss': 0.0}
        
        # Sample batch
        batch = random.sample(self.buffer, min(self.batch_size, len(self.buffer)))
        
        # Compute loss based on algorithm
        if self.algorithm == 'ppo':
            losses = self.compute_ppo_loss(batch)
            total_loss = losses['total_loss']
        else:
            # Simplified loss for other algorithms
            states = torch.FloatTensor(np.array([t['state'] for t in batch])).to(self.device)
            actions = torch.FloatTensor(np.array([t['action'] for t in batch])).to(self.device)
            rewards = torch.FloatTensor([t['reward'] for t in batch]).to(self.device)
            
            action_probs, _ = self.model(states)
            predicted_rewards = torch.sum(action_probs * actions, dim=1)
            total_loss = F.mse_loss(predicted_rewards, rewards)
            losses = {'total_loss': total_loss}
        
        # Backward pass
        self.optimizer.zero_grad()
        if hasattr(self, 'value_optimizer'):
            self.value_optimizer.zero_grad()
        
        total_loss.backward()
        
        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.max_grad_norm)
        if hasattr(self, 'value_network'):
            torch.nn.utils.clip_grad_norm_(self.value_network.parameters(), self.max_grad_norm)
        
        self.optimizer.step()
        if hasattr(self, 'value_optimizer'):
            self.value_optimizer.step()
        
        return {k: v.item() if isinstance(v, torch.Tensor) else v for k, v in losses.items()}
    
    def train_episode(self, episode: int) -> Dict[str, float]:
        """Train for one episode"""
        state = self.env.reset()
        episode_reward = 0.0
        episode_loss = 0.0
        step_count = 0
        
        while True:
            # Select action
            action, action_info = self.select_action(state, training=True)
            
            # Take step in environment
            next_state, env_reward, done, info = self.env.step(action)
            
            # Calculate enhanced reward
            portfolio_return = info.get('portfolio_return', env_reward)
            portfolio_value = info.get('portfolio_value', 1.0)
            reward_components = self.reward_function.calculate_reward(
                portfolio_return, portfolio_value, action
            )
            enhanced_reward = reward_components['total_reward']
            
            # Store transition
            self.store_transition(state, action, enhanced_reward, next_state, done, action_info)
            
            # Update model
            if len(self.buffer) >= self.batch_size and step_count % 10 == 0:
                loss_info = self.update_model()
                episode_loss += loss_info.get('total_loss', 0.0)
            
            episode_reward += enhanced_reward
            state = next_state
            step_count += 1
            
            if done:
                break
        
        # Calculate episode metrics
        episode_metrics = {
            'reward': episode_reward,
            'loss': episode_loss / max(step_count // 10, 1),
            'steps': step_count,
            'portfolio_value': info.get('portfolio_value', 1.0),
            'sharpe_ratio': self.calculate_episode_sharpe_ratio()
        }
        
        # Update learning rate
        self.lr_scheduler.step(episode_reward, episode, self.optimizer)
        
        return episode_metrics
    
    def calculate_episode_sharpe_ratio(self) -> float:
        """Calculate Sharpe ratio for recent episodes"""
        if len(self.reward_function.returns_history) < 10:
            return 0.0
        
        returns = np.array(self.reward_function.returns_history)
        if np.std(returns) < 1e-8:
            return 0.0
        
        return np.mean(returns) / np.std(returns)
    
    def evaluate(self, env, num_episodes: int = 10) -> Dict[str, float]:
        """Evaluate model performance"""
        self.model.eval()
        
        episode_rewards = []
        episode_returns = []
        episode_sharpe_ratios = []
        
        for _ in range(num_episodes):
            state = env.reset()
            episode_reward = 0.0
            returns = []
            
            while True:
                action, _ = self.select_action(state, training=False)
                next_state, reward, done, info = env.step(action)
                
                episode_reward += reward
                returns.append(info.get('portfolio_return', reward))
                state = next_state
                
                if done:
                    break
            
            episode_rewards.append(episode_reward)
            episode_returns.append(np.sum(returns))
            
            if len(returns) > 1:
                sharpe = np.mean(returns) / (np.std(returns) + 1e-8)
                episode_sharpe_ratios.append(sharpe)
        
        self.model.train()
        
        return {
            'avg_reward': np.mean(episode_rewards),
            'std_reward': np.std(episode_rewards),
            'avg_return': np.mean(episode_returns),
            'avg_sharpe': np.mean(episode_sharpe_ratios) if episode_sharpe_ratios else 0.0,
            'total_return': np.mean(episode_returns),
            'max_drawdown': 0.0,  # Placeholder
            'win_rate': np.mean(np.array(episode_returns) > 0),
            'volatility': np.std(episode_returns)
        }
    
    def train(self,
              env,
              num_episodes: int = 1000,
              eval_frequency: int = 50,
              early_stopping_patience: int = 100,
              validation_env=None) -> pd.DataFrame:
        """
        Train the model
        
        Args:
            env: Training environment
            num_episodes: Number of episodes to train
            eval_frequency: Frequency of evaluation
            early_stopping_patience: Patience for early stopping
            validation_env: Validation environment
            
        Returns:
            DataFrame with training history
        """
        self.logger.info(f"Starting enhanced training for {num_episodes} episodes")
        
        training_history = []
        best_reward = float('-inf')
        episodes_without_improvement = 0
        
        for episode in tqdm(range(num_episodes), desc="Training"):
            # Train episode
            episode_metrics = self.train_episode(episode)
            
            # Log to monitor
            if self.monitor:
                self.monitor.log_episode(
                    episode=episode,
                    reward=episode_metrics['reward'],
                    loss=episode_metrics['loss'],
                    epsilon=self.lr_scheduler.current_lr,  # Use LR as exploration proxy
                    additional_metrics={
                        'steps': episode_metrics['steps'],
                        'portfolio_value': episode_metrics['portfolio_value'],
                        'sharpe_ratio': episode_metrics['sharpe_ratio']
                    }
                )
            
            # Store metrics
            training_history.append({
                'episode': episode,
                'reward': episode_metrics['reward'],
                'loss': episode_metrics['loss'],
                'sharpe_ratio': episode_metrics['sharpe_ratio'],
                'learning_rate': self.lr_scheduler.current_lr
            })
            
            # Evaluation
            if episode % eval_frequency == 0 and episode > 0:
                eval_env = validation_env if validation_env else env
                eval_metrics = self.evaluate(eval_env, num_episodes=5)
                
                self.logger.info(
                    f"Episode {episode}: "
                    f"Reward={episode_metrics['reward']:.4f}, "
                    f"Loss={episode_metrics['loss']:.4f}, "
                    f"Eval_Sharpe={eval_metrics['avg_sharpe']:.4f}"
                )
                
                # Early stopping check
                if eval_metrics['avg_reward'] > best_reward:
                    best_reward = eval_metrics['avg_reward']
                    episodes_without_improvement = 0
                    
                    # Save best model
                    self.save_model(f'models/best_model_{self.timestamp}.pth')
                else:
                    episodes_without_improvement += eval_frequency
                
                if episodes_without_improvement >= early_stopping_patience:
                    self.logger.info(f"Early stopping at episode {episode}")
                    break
        
        # Final evaluation and cleanup
        if self.monitor:
            self.monitor.close()
        
        return pd.DataFrame(training_history)
    
    def save_model(self, path: str):
        """Save model and training state"""
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        save_dict = {
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'algorithm': self.algorithm,
            'timestamp': self.timestamp
        }
        
        if hasattr(self, 'value_network'):
            save_dict['value_network_state_dict'] = self.value_network.state_dict()
            save_dict['value_optimizer_state_dict'] = self.value_optimizer.state_dict()
        
        torch.save(save_dict, path)
        self.logger.info(f"Model saved to {path}")
    
    def load_model(self, path: str):
        """Load model and training state"""
        checkpoint = torch.load(path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if 'value_network_state_dict' in checkpoint and hasattr(self, 'value_network'):
            self.value_network.load_state_dict(checkpoint['value_network_state_dict'])
            self.value_optimizer.load_state_dict(checkpoint['value_optimizer_state_dict'])
        
        self.logger.info(f"Model loaded from {path}")


def create_enhanced_trainer(env, model, device, algorithm: str = 'ppo', **kwargs) -> EnhancedPortfolioTrainer:
    """
    Create an enhanced portfolio trainer
    
    Args:
        env: Trading environment
        model: Neural network model
        device: Computation device
        algorithm: RL algorithm to use
        **kwargs: Additional arguments
        
    Returns:
        EnhancedPortfolioTrainer: Configured trainer
    """
    return EnhancedPortfolioTrainer(
        env=env,
        model=model,
        device=device,
        algorithm=algorithm,
        **kwargs
    )
