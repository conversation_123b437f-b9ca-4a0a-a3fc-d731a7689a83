# AlphaPortfolio-A股实现

基于论文《AlphaPortfolio: Direct Construction Through Deep Reinforcement Learning and Interpretable AI》的A股市场强化学习量化投资框架。

## 项目概述

本项目使用深度强化学习直接构建投资组合，通过跨资产注意力网络(CAAN)捕捉不同股票间关系，优化投资组合的夏普比率和回报。模型完全符合原论文架构，同时针对A股市场特点进行了适配和优化。

## 项目结构

```
.
├── src/
│   ├── ablation_studies.py  # 消融实验研究模块
│   ├── data_processor.py    # 数据处理模块，包含A股特定的因子计算
│   ├── models.py            # 模型架构，包含AlphaPortfolio和CAAN实现
│   ├── environment.py       # 交易环境，模拟A股市场交易规则
│   ├── trainer.py           # 强化学习训练器，包含经验回放和策略优化
│   ├── supervised_models.py # 监督学习模型实现
│   ├── tester.py            # 模型测试模块
│   ├── visualization.py     # 可视化工具
│   └── main.py              # 主程序，提供命令行接口
├── dataset/                 # 数据集目录
│   ├── hs300/               # 沪深300成分股数据
│   ├── zz500/               # 中证500成分股数据
│   └── zz1000/              # 中证1000成分股数据
├── models/                  # 模型保存目录
├── results/                 # 结果输出目录，存储投资组合权重和分析报告
├── logs/                    # 日志文件目录
└── requirements.txt         # 项目依赖文件
```

## 主要特性

- **符合论文架构**：严格实现了论文中提出的跨资产注意力网络(CAAN)结构
- **鲁棒性增强**：增加了NaN检测与处理、梯度裁剪和数值稳定性优化
- **完善日志系统**：使用Python标准日志库，便于追踪训练过程和调试
- **灵活配置**：支持多种指数成分股、不同时间范围和各种模型参数配置
- **进度可视化**：提供详细的训练进度条和投资组合分析工具
- **消融实验支持**：提供完整的消融实验框架，便于研究各组件贡献

## 环境要求

- Python 3.7+
- PyTorch 1.7.0+
- pandas, numpy, gym, tqdm
- matplotlib, seaborn (用于可视化)
- scikit-learn (用于数据处理)

## 安装

1. 克隆仓库：
```bash
git clone [repository_url]
cd alphaportfolio-ashare
```

2. 创建虚拟环境：
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 训练模型（完整模型）

```bash
# 简单训练
python src/main.py --index_type hs300 --start_year 2019 --end_year 2024 --num_episodes 3 --window_size 20 --batch_size 128 --top_stocks 15 --eval_frequency 1 --G 10
```

### 2. 测试模型（完整模型）

```bash
# 简单测试
python src/tester.py --test_year 2025 --model_path models/episode_17.pth --top_stocks 15  --G 10
```

### 3. 运行消融实验

```bash
# 运行所有消融实验（训练 + 验证）
python src/run_ablation.py --experiment_type all --start_year 2019 --end_year 2023 --top_stocks 4 --use_core_features --num_episodes 2 --window_size 10 --batch_size 16 --G 2

# 测试消融实验（测试）
python src/test_ablation.py --test_year 2024 --model_dir results/ablation_20250508_174424/models --top_stocks 4 --use_core_features --G 2
```

## 消融实验说明

### 1. 模型架构消融实验
- **完整模型**：包含 Transformer Encoder 和 CAAN
- **Transformer Encoder**：仅使用 Transformer 编码器
- **TE + CAAN**：Transformer Encoder 与 CAAN 的组合

### 2. 训练方法消融实验
- **深度强化学习 (DRL)**：使用 PPO 算法
- **监督学习**：使用传统监督学习方法

### 3. 特征消融实验
- **所有特征**：使用完整的特征集
- **核心特征**：使用核心特征子集

### 4. 投资组合消融实验
- **多头+空头组合**：允许做多和做空
- **仅多头组合**：只允许做多

### 5. 市场条件消融实验
- **牛市环境**：在上涨市场中的表现
- **熊市环境**：在下跌市场中的表现

### 6. 经济约束消融实验
- **包含交易成本**：考虑交易费用
- **忽略交易成本**：不考虑交易费用

## 参数说明

### 基本参数
- `--index_type`: 指数类型 (hs300/zz500/zz1000)
- `--start_year`: 训练数据起始年份
- `--end_year`: 训练数据结束年份
- `--test_year`: 测试年份

### 模型参数
- `--num_episodes`: 训练回合数
- `--window_size`: 观察窗口大小 (默认: 50)
- `--batch_size`: 批次大小 (默认: 64)
- `--learning_rate`: 学习率 (默认: 0.0001)
- `--gamma`: 折扣因子 (默认: 0.99)
- `--G`: 多空组合的G参数，决定多头和空头各自的资产数量 (默认: 10)

### 环境参数
- `--initial_balance`: 初始资金 (默认: 1000000)
- `--transaction_cost_pct`: 交易成本百分比 (默认: 0.001)
- `--top_stocks`: 使用交易量最大的前N支股票
- `--use_core_features`: 使用核心特征子集
- `--feature_list`: 自定义特征列表

## 数据要求

### 数据目录结构
```
dataset/
├── hs300/
│   └── YYYY-MM-DD_YYYY-MM-DD/
│       └── factors_hs300_YYYYMMDD_YYYYMMDD.csv
├── zz500/
└── zz1000/
```

### 数据文件格式
CSV文件必须包含以下列：
- `trade_date`: 交易日期
- `ts_code`: 股票代码
- `close`: 收盘价
- `open`: 开盘价
- `high`: 最高价
- `low`: 最低价
- `total_vol`: 成交量
- `turnover`: 换手率

## 注意事项

1. **数据准备**
   - 确保数据集目录结构正确
   - 特征命名需与代码中的映射一致
   - 数据预处理和清洗很重要

2. **模型训练**
   - 建议使用GPU进行训练
   - 根据实际情况调整超参数
   - 注意监控训练过程中的损失值

3. **消融实验**
   - 建议先使用小规模数据测试
   - 注意内存使用，特别是在处理大量股票数据时
   - 可以根据需要选择运行特定的消融实验

4. **风险提示**
   - 模型仅供研究使用
   - 实盘交易需要更多风险控制措施
   - 建议进行充分的回测和验证

## 常见问题

1. **内存不足**
   - 减少 `top_stocks` 数量
   - 使用 `use_core_features` 选项
   - 减小 `batch_size` 和 `window_size`

2. **训练不稳定**
   - 检查数据质量
   - 调整学习率
   - 使用梯度裁剪

3. **模型性能问题**
   - 检查特征工程
   - 调整模型参数
   - 增加训练轮数

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License 